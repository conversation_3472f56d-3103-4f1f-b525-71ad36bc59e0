import { ProductStock } from './models';

export const PRODUCTS: ProductStock[] = [
  {
    productsID: 'P001',
    productName: 'Item 1',
    stock: 10,
    price: '$100',
    status: 'In Stock',
  },
  {
    productsID: 'P002',
    productName: 'Item 2',
    stock: 0,
    price: '$150',
    status: 'Out of Stock',
  },
  {
    productsID: 'P003',
    productName: 'Blouse Ruffle Tube top',
    stock: 145,
    price: '$14.99',
    status: 'Low Stock',
  },
  {
    productsID: 'P004',
    productName: 'Gold-colored locket watch',
    stock: 569,
    price: '$19.99',
    status: 'In Stock',
  },
  {
    productsID: 'P005',
    productName: 'Crop top Sweater Clothing',
    stock: 541,
    price: '$22.49',
    status: 'In Stock',
  },
  {
    productsID: 'P006',
    productName: 'Sleeve Clothing Leggings',
    stock: 126,
    price: '$31.78',
    status: 'Low Stock',
  },
  {
    productsID: 'P007',
    productName: 'Yellow women shoes',
    stock: 0,
    price: '$49.99',
    status: 'Out of Stock',
  },
  {
    productsID: 'P008',
    productName: 'Straw hat Cap Cowboy',
    stock: 571,
    price: '$49.99',
    status: 'In Stock',
  },
  {
    productsID: 'P009',
    productName: 'Sneakers Shoe Nike Basketball',
    stock: 0,
    price: '$49.99',
    status: 'Out of Stock',
  },
  {
    productsID: 'P010',
    productName: 'Modern Fashion T shirt',
    stock: 321,
    price: '$29.49',
    status: 'In Stock',
  },
  {
    productsID: 'P011',
    productName: 'Footwear',
    stock: 478,
    price: '$74.99',
    status: 'In Stock',
  },
  {
    productsID: 'P012',
    productName: 'Yellow women shoes',
    stock: 54,
    price: '$59.79',
    status: 'Low Stock',
  },
  {
    productsID: 'P013',
    productName: 'Yellow women shoes',
    stock: 0,
    price: '$49.99',
    status: 'Out of Stock',
  },
  {
    productsID: 'P014',
    productName: 'Straw hat Cap Cowboy',
    stock: 571,
    price: '$49.99',
    status: 'In Stock',
  },
];
