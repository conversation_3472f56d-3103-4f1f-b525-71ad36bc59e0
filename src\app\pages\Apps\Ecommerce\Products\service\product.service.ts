import { Injectable } from '@angular/core';
import { Products } from '../interfaces/product.model';
import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ProductService {
  private products: Products[] = [
    {
      productName: 'Blouse Ruffle Tube top',
      category: 'Fashion',
      price: 10,
      revenue: '$15,236',
      qty: 154,
      image: 'assets/images/products/img-01.png',
      image1: 'assets/images/products/img-02.png',
      image2: 'assets/images/products/img-03.png',
      status: true,
      brand: 'Gucci',
      description: 'lorem ipsm',
      gender: 'female',
      size: ['S', 'M', 'XL', 'XXL'],
      colores: ['green','red','pink','purple'],
      cashOnDelivery: true,
      visaAndMaster: true,
      bankTransfer: false,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19115',
      count: 1
    },
    {
      productName: 'Gold-colored locket watch',
      category: 'Watch',
      price: 10,
      revenue: '$18,956',
      qty: 187,
      image: 'assets/images/products/img-02.png',
      image1: 'assets/images/products/img-01.png',
      image2: 'assets/images/products/img-03.png',
      status: true,
      brand: 'Gucci',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['2', '3','4','5','6','7'],
      colores: ['blue', 'green', 'red','pink','purple'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 5,
      discount: 1,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19116',
    },
    {
      productName: 'Spar Men Black Running Shoes',
      category: 'Footwear',
      price: 35,
      revenue: '$0',
      qty: 487,
      image: 'assets/images/products/img-03.png',
      image1: 'assets/images/products/img-01.png',
      image2: 'assets/images/products/img-02.png',
      status: false,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['2','3','4','5'],
      colores: ['sky', 'red'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19117',
    },
    {
      productName: 'Crop top Sweater Clothing',
      category: 'Fashion',
      price: 29,
      revenue: '$4,265',
      qty: 177,
      image: 'assets/images/products/img-04.png',
      image1: 'assets/images/products/img-01.png',
      image2: 'assets/images/products/img-02.png',
      status: false,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL'],
      colores: ['sky', 'red','pink','purple','green'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19118',
    },
    {
      productName: 'Sleeve Clothing Leggings',
      category: 'Fashion',
      price: 22,
      revenue: '$7,465',
      qty: 183,
      image: 'assets/images/products/img-05.png',
      image1: 'assets/images/products/img-04.png',
      image2: 'assets/images/products/img-03.png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL'],
      colores: ['sky', 'red'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19119',
    },
    {
      productName: 'Bra Lace Crop top',
      category: 'Fashion',
      price: 29,
      revenue: '$9,613',
      qty: 326,
      image: 'assets/images/products/img-06.png',
      image1: 'assets/images/products/img-01.png',
      image2: 'assets/images/products/img-02 .png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL','2XL'],
      colores: ['sky', 'red','pink','purple','green'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19120',
    },
    {
      productName: 'Yellow women shoes',
      category: 'Footwear',
      price: 36,
      revenue: '$11,074',
      qty: 147,
      image: 'assets/images/products/img-07.png',
      image1: 'assets/images/products/img-04.png',
      image2: 'assets/images/products/img-05.png',
      status: false,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['XS','S','L', 'XL'],
      colores: ['sky', 'red','blue','yellow','purple'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19121',
    },
    {
      productName: 'Tote bag Leather Handbag Dolce',
      category: 'Bags',
      price: 79,
      revenue: '$19,803',
      qty: 98,
      image: 'assets/images/products/img-08.png',
      image1: 'assets/images/products/img-01.png',
      image2: 'assets/images/products/img-05.png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL'],
      colores: ['sky', 'red'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19122',
    },
    {
      productName: 'Hoodie Jacket Letterman Sleeve Coat',
      category: 'Fashion',
      price: 44,
      revenue: '$9,961',
      qty: 246,
      image: 'assets/images/products/img-09.png',
      image1: 'assets/images/products/img-07.png',
      image2: 'assets/images/products/img-05.png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL','2XL'],
      colores: ['sky','red','pink','purple','green'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19123',
    },
    {
      productName: 'Straw hat Cap Cowboy hat Sun hat',
      category: 'Accessories',
      price: 24,
      revenue: '$6,087',
      qty: 213,
      image: 'assets/images/products/img-10.png',
      image1: 'assets/images/products/img-4.png',
      image2: 'assets/images/products/img-8.png',
      status: false,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L','XL','XS','2XL'],
      colores: ['sky', 'red','purple','blue','yellow'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19124',
    },
    {
      productName: 'Sneakers Shoe Nike Basketball',
      category: 'Footwear',
      price: 32,
      revenue: '$14,872',
      qty: 198,
      image: 'assets/images/products/img-11.png',
      image1: 'assets/images/products/img-1.png',
      image2: 'assets/images/products/img-11.png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['S','L', 'XL'],
      colores: ['sky', 'red','yellow','pink'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19125',
    },
    {
      productName: 'Bermuda shorts Denim Jeans Waist',
      category: 'Fashion',
      price: 24,
      revenue: '$5,108',
      qty: 54,
      image: 'assets/images/products/img-12.png',
      image2: 'assets/images/products/img-5.png',
      image1: 'assets/images/products/img-2.png',
      status: false,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL'],
      colores: ['sky', 'red','blue','yellow'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19126',
    },
    {
      productName: 'Jean jacket Denim Levi Strauss & Co. Jeans',
      category: 'Fashion',
      price: 39,
      revenue: '$9,780',
      qty: 119,
      image: 'assets/images/products/img-13.png',
      image1: 'assets/images/products/img-1.png',
      image2: 'assets/images/products/img-3.png',
      status: true,
      brand: 'Zara',
      description: 'lorem ipsm',
      gender: 'male',
      size: ['L', 'XL'],
      colores: ['sky', 'red','blue','yellow','purple'],
      cashOnDelivery: false,
      visaAndMaster: false,
      bankTransfer: true,
      stock: 7,
      discount: 10,
      sellingPrice: 5,
      checked: false,
      productID: 'PEP-19127',
    },
  ];

  constructor() {}

  getProducts(): Observable<Products[]> {
    return of(this.products);
  }

  addProduct(product: Products): Observable<Products> {
    if (Array.isArray(this.products)) {
      this.products = [product, ...this.products];
    }
    return of(product);
  }

  updateProduct(updatedProduct: Products): Observable<Products> {
    const updatedProducts = this.products.map((product) =>
      product.productID === updatedProduct.productID ? updatedProduct : product
    );

    this.products = [...updatedProducts];

    return of(updatedProduct);
  }

  deleteProduct(id: string): Observable<void> {
    this.products = this.products.filter((p) => p.productID !== id);
    return of(void 0);
  }
}
