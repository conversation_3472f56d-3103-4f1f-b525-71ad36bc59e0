import {
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { provideRouter } from '@angular/router';
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';
import { ProductEffects } from './pages/Apps/Ecommerce/Products/store/effects/product.effects';
import { productReducer } from './pages/Apps/Ecommerce/Products/store/reducers/product.reducer';
import { NgxMaskOptions, provideEnvironmentNgxMask } from 'ngx-mask';
import {
  Activity,
  Airplay,
  AlertTriangle,
  Apple,
  ArrowDownFromLine,
  ArrowUpFromDot,
  Atom,
  AudioLines,
  BadgeCheck,
  BarChartBig,
  Bell,
  BookOpen,
  Box,
  Brain,
  BriefcaseBusiness,
  Building2,
  Cake,
  Calendar,
  CalendarCheck,
  CalendarClock,
  CalendarDays,
  CalendarPlus,
  CalendarRange,
  Camera,
  Check,
  CheckSquare,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  CircleArrowUp,
  CircleCheckBig,
  CircleDot,
  CircleDotDashed,
  CirclePlay,
  CirclePlus,
  Clapperboard,
  Clipboard,
  CloudUpload,
  Coins,
  Columns3,
  Columns4,
  Computer,
  CornerDownRight,
  CornerUpLeft,
  CornerUpRight,
  CreditCard,
  Cross,
  Diamond,
  Crown,
  Dot,
  Download,
  Dribbble,
  Droplet,
  Ellipsis,
  Eye,
  EyeOff,
  Facebook,
  Fan,
  Feather,
  File,
  FileText,
  Fingerprint,
  FlaskConical,
  Flower,
  FolderClosed,
  FolderOpen,
  GalleryVerticalEnd,
  Gem,
  Github,
  Gitlab,
  Globe,
  GraduationCap,
  HandCoins,
  HandMetal,
  Handshake,
  Headset,
  HeartPulse,
  Home,
  Image,
  Inbox,
  Instagram,
  JapaneseYen,
  Landmark,
  Layers3,
  Linkedin,
  List,
  ListTree,
  LogOut,
  Mail,
  MailOpen,
  MapPin,
  Medal,
  MessageCircleMore,
  MessageCircleQuestion,
  MessageSquareText,
  MessagesSquare,
  Microscope,
  Monitor,
  MonitorDot,
  MonitorStop,
  Moon,
  Mouse,
  MousePointerClick,
  MoveLeft,
  MoveRight,
  PanelBottomDashed,
  Pencil,
  PencilRuler,
  Phone,
  Pill,
  Play,
  Plus,
  Pyramid,
  Quote,
  Redo2,
  Route,
  Ruler,
  Scale,
  ScrollText,
  Search,
  SendHorizontal,
  Settings,
  ShieldCheck,
  ShoppingBag,
  ShoppingCart,
  Shuffle,
  Siren,
  SlidersHorizontal,
  Smile,
  Sparkles,
  SquareUser,
  Star,
  Stethoscope,
  Sun,
  SunMedium,
  Syringe,
  Tangent,
  Trash,
  TrendingDown,
  TrendingUp,
  Truck,
  Twitch,
  Twitter,
  Upload,
  User,
  UserRound,
  Users,
  Video,
  Wrench,
  X,
  Youtube,
  Zap,
  Link,
  AlignLeft,
  Hourglass,
  Dessert,
  BriefcaseMedical,
  Ambulance,
  Trash2,
  Filter,
  ImagePlus,
  Minus,
  CircleAlert,
  TicketMinus,
  LucideAngularModule,
  Send,
  FileArchive,
  Briefcase,
  Archive,
  Blend,
  Container,
  Heart,
  Store,
  Rotate3D,
  RotateCcw,
  CircleArrowDown,
  Clock,
  Octagon,
  OctagonAlert,
  Reply,
  ArrowLeft,
  Clock3,
  MicOff,
  Mic,
  Pause,
  Disc,
  CalendarPlus2,
  CalendarX2,
  Printer,
  BellRing,
  Percent,
  UserRoundPlus,
  PartyPopper,
  PanelRightOpen,
  LayoutGrid,
  BellDot,
  Presentation,
  Gauge,
  Folders,
  Shapes,
  ScatterChart,
  Dna,
  Map,
  Trophy,
  Hospital,
  School,
  UsersRound,
  AlignStartVertical,
  KeyRound,
  RemoveFormatting,
  TextQuote,
  Table2,
  BarChart3,
  LifeBuoy,
  HeartOff,
  VideoOff,
  PhoneMissed,
  ShoppingBasket,
  HeartHandshake,
  BookOpenText,
  NotepadTextDashed,
  LibraryBig,
  NotebookText,
  SquareMousePointer,
  SquareArrowOutUpRight,
  UserRoundCheck,
  UserX,
  Droplets,
  MonitorSmartphone,
  Smartphone,
  MoveUp,
  Volume1,
  Volume2,
  Pin,
  Captions,
  CaptionsOff,
  ArrowUpRight,
  ArrowUpLeft,
  ArrowDownToLine,
  VenetianMask,
  PackageOpen,
  PanelsTopLeft,
  LayoutList,
  CalendarFold,
  Aperture,
  LocateFixed,
  Rotate3d,
  BadgeDollarSign,
} from 'lucide-angular';
import { InvoicedatasReducer } from './pages/Apps/Invoice/store/reducers/invoice.reducer';
import { InvoicedatasEffects } from './pages/Apps/Invoice/store/effects/invoice.effects';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';

const LucideIcons = {
  Activity,
  Airplay,
  AlertTriangle,
  Percent,
  Apple,
  ArrowDownFromLine,
  ArrowUpFromDot,
  Atom,
  AudioLines, Rotate3D,
  BadgeCheck,
  BarChartBig,
  Bell,
  BookOpen,
  Box,
  Brain,
  BriefcaseBusiness,
  Building2,
  Cake,
  Calendar,
  CalendarCheck,
  CalendarClock,
  CalendarDays,
  Printer,
  CalendarPlus,
  CalendarRange,
  Camera,
  Check,
  CheckSquare,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  CircleArrowUp,
  CircleCheckBig,
  CircleDot,
  Store,
  CircleDotDashed,
  CirclePlay,
  CirclePlus,
  Clapperboard,
  Clipboard,
  CloudUpload,
  Coins,
  Columns3,
  Columns4,
  Computer,
  CornerDownRight,
  CornerUpLeft,
  CornerUpRight,
  CreditCard,
  Cross,
  Diamond,
  Crown,
  Dot,
  Download,
  Dribbble,
  Droplet,
  Ellipsis,
  Eye,
  EyeOff,
  Facebook,
  Fan,
  Feather,
  LayoutList,
  File,
  FileText,
  Fingerprint,
  FlaskConical,
  Flower,
  FolderClosed,
  FolderOpen,
  GalleryVerticalEnd,
  Gem,
  Github,
  Gitlab,
  Globe,
  GraduationCap,
  HandCoins,
  HandMetal,
  Handshake,
  Headset,
  HeartPulse,
  Home,
  Image,
  Inbox,
  Instagram,
  JapaneseYen,
  Landmark,
  Layers3,
  Linkedin,
  List,
  ListTree,
  LogOut,
  Mail,
  MailOpen,
  MapPin,
  Medal,
  MessageCircleMore,
  MessageCircleQuestion,
  MessageSquareText,
  MessagesSquare,
  Microscope,
  Monitor,
  MonitorDot,
  MonitorStop,
  Moon,
  Mouse,
  MousePointerClick,
  MoveLeft,
  MoveRight,
  PanelBottomDashed,
  Pencil,
  PencilRuler,
  Phone,
  Pill,
  Play,
  Plus,
  Pyramid,
  Quote,
  Redo2,
  Route,
  Ruler,
  Heart,
  Scale,
  ScrollText,
  Search,
  SendHorizontal,
  Settings,
  ShieldCheck,
  ShoppingBag,
  ShoppingCart,
  RotateCcw,
  Shuffle,
  Siren,
  SlidersHorizontal,
  Smile,
  Sparkles,
  SquareUser,
  Star,
  Stethoscope,
  Sun,
  SunMedium,
  Syringe,
  Tangent,
  Trash,
  TrendingDown,
  TrendingUp,
  Truck,
  Twitch,
  Twitter,
  Upload,
  User,
  UserRound,
  Users,
  Video,
  Wrench,
  X,
  Youtube,
  Zap,
  Link,
  AlignLeft,
  Hourglass,
  Dessert,
  BriefcaseMedical,
  Ambulance,
  Trash2,
  Filter,
  ImagePlus,
  Minus,
  CircleAlert,
  TicketMinus,
  Send,
  FileArchive,
  Briefcase,
  Archive,
  Blend,
  Container,
  CircleArrowDown,
  Clock,
  Octagon,
  OctagonAlert,
  Reply,
  ArrowLeft,
  Clock3,
  MicOff,
  Mic,
  Pause,
  Disc,
  CalendarPlus2,
  CalendarX2,
  BellRing,
  UserRoundPlus,
  PartyPopper,
  LayoutGrid,
  BellDot,
  Presentation,
  Shapes,
  ScatterChart,
  Dna,
  Gauge,
  Map,
  Folders,
  Trophy,
  Hospital,
  School,
  UsersRound,
  AlignStartVertical,
  KeyRound,
  RemoveFormatting,
  TextQuote,
  Table2,
  BarChart3,
  LifeBuoy,
  HeartOff,
  PanelRightOpen,
  VideoOff,
  PhoneMissed,
  ShoppingBasket,
  HeartHandshake,
  BookOpenText,
  NotepadTextDashed,
  LibraryBig,
  NotebookText,
  SquareMousePointer,
  SquareArrowOutUpRight,
  UserRoundCheck,
  UserX,
  Droplets,
  MonitorSmartphone,
  Smartphone,
  MoveUp,
  Volume1,
  Volume2,
  Pin,
  Captions,
  CaptionsOff,
  ArrowUpRight,
  ArrowUpLeft,
  ArrowDownToLine,
  VenetianMask,
  PackageOpen,
  PanelsTopLeft,
  CalendarFold,
  Aperture,
  LocateFixed,
  Rotate3d,
  BadgeDollarSign,

};

const maskConfig: NgxMaskOptions = {
  validation: false,
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideClientHydration(),
    provideHttpClient(),
    provideAnimations(),
    provideStore({
      products: productReducer,
      Invoicedatas: InvoicedatasReducer,
    }),
    // Provide all the effects
    provideEffects(
      ProductEffects,
      InvoicedatasEffects
    ),
    importProvidersFrom(
      LucideAngularModule.pick(LucideIcons),
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
        defaultLanguage: 'en',
      })
    ),
    provideEnvironmentNgxMask(maskConfig),
  ],
};
