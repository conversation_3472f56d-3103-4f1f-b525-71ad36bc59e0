export const menu = [
  {
    separatorText: 'pe-menu',
    title: 'pe-dashboard',
    icon: 'aperture',
    link: '/',
    children: [
    ],
  },
  {
    title: 'pe-frontend',
    lang: 'pe-frontend',
    icon: 'airplay',
    link: 'https://srbthemes.kcubeinfotech.com/domiex/angular/landing/',
    children: []
  },
  {
    separatorText: 'pe-apps',
    title: 'pe-products',
    link: '#',
    lang: 'pe-products',
    icon: 'sparkles',
    children: [
      {
        title: 'pe-list-view',
        lang: 'pe-list-view',
        link: '/apps-ecommerce-products-list',
        children: [],
      },
      {
        title: 'pe-grid-view',
        lang: 'pe-grid-view',
        link: '/apps-ecommerce-products-grid',
        children: [],
      },
      {
        title: 'pe-add-new',
        lang: 'pe-add-new',
        link: '/apps-ecommerce-create-products',
        children: [],
      },
      {
        title: 'pe-overview',
        lang: 'pe-overview',
        link: '/apps-ecommerce-product-overview',
        children: [],
      },
    ],
  },
  {
    title: 'pe-category',
    lang: 'pe-category',
    icon: 'box',
    link: '/apps-ecommerce-category',
    children: [],
  },
  {
    title: 'pe-orders',
    link: '#',
    lang: 'pe-orders',
    icon: 'shopping-basket',
    children: [
      {
        title: 'pe-list-view',
        lang: 'pe-list-view',
        link: '/apps-ecommerce-orders-list',
        children: [],
      },
      {
        title: 'pe-overview',
        lang: 'pe-overview',
        link: '/apps-ecommerce-orders-overview',
        children: [],
      },
    ],
  },
  {
    title: 'pe-customers',
    link: '#',
    lang: 'pe-customers',
    icon: 'messages-square',
    children: [
      {
        title: 'pe-list-view',
        lang: 'pe-list-view',
        link: '/apps-ecommerce-customer-list',
        children: [],
      },
      {
        title: 'pe-overview',
        lang: 'pe-overview',
        link: '/apps-ecommerce-customer-user',
        children: [],
      },
    ],
  },
  {
    title: 'pe-track-order',
    lang: 'pe-track-order',
    icon: 'locate-fixed',
    link: '/apps-ecommerce-orders-track',
    children: [],
  },
  {
    title: 'pe-shop-cart',
    lang: 'pe-shop-cart',
    link: '/apps-ecommerce-shop-cart',
    icon: 'shopping-bag',
    children: [],
  },
  {
    title: 'pe-checkout',
    lang: 'pe-checkout',
    link: '/apps-ecommerce-checkout',
    icon: 'rotate-3d',
    children: [],
  },
  {
    title: 'pe-wishlist',
    lang: 'pe-wishlist',
    link: '/apps-ecommerce-wishlist',
    icon: 'heart',
    children: [],
  },
  {
    title: 'pe-payment',
    lang: 'pe-payment',
    link: '/apps-ecommerce-payment',
    icon: 'badge-dollar-sign',
    children: [],
  },
  {
    title: 'pe-manage-reviews',
    lang: 'pe-manage-reviews',
    link: '/apps-ecommerce-manage-reviews',
    icon: 'star',
    children: [],
  },
  {
    title: 'pe-invoice',
    lang: 'pe-invoice',
    icon: 'file-text',
    link: '#',
    children: [
      {
        title: 'pe-list-view',
        lang: 'pe-list-view',
        link: '/apps-invoice-list',
        children: [],
      },
      {
        title: 'pe-grid-view',
        lang: 'pe-grid-view',
        link: '/apps-invoice-grid',
        children: [],
      },
      {
        title: 'pe-add-new',
        lang: 'pe-add-new',
        link: '/apps-invoice-create',
        children: [],
      },
      {
        title: 'pe-overview-1',
        lang: 'pe-overview-1',
        link: '/apps-invoice-overview-1',
        children: [],
      },
      {
        title: 'pe-overview-2',
        lang: 'pe-overview-2',
        link: '/apps-invoice-overview-2',
        children: [],
      },
    ],
  },
  {
    separatorText: 'pe-pages',
    title: 'pe-authentication',
    lang: 'pe-authentication',
    icon: 'users-round',
    link: '#',
    children: [
      {
        title: 'pe-sign-in',
        link: '/auth-signin-basic',
        lang: 'pe-sign-in',
        children: [
        ],
      },
      {
        title: 'pe-sign-up',
        link: '/auth-signup-basic',
        lang: 'pe-sign-up',
        children: [
        ],
      },
      {
        title: 'pe-forgot-password',
        link: '/auth-forgot-password-basic',
        lang: 'pe-forgot-password',
        children: [
        ],
      },
      {
        title: 'pe-two-step-verification',
        link: '/auth-two-step-verification-basic',
        lang: 'pe-two-step-verification',
        children: [
        ],
      },
      {
        title: 'pe-reset-password',
        link: '/auth-reset-password-basic',
        lang: 'pe-reset-password',
        children: [
        ],
      },
      {
        title: 'pe-successful-password',
        link: '/auth-successful-password-basic',
        lang: 'pe-successful-password',
        children: [
        ],
      },
      {
        title: 'pe-account-deactivation',
        link: '/auth-account-deactivation-basic',
        lang: 'pe-account-deactivation',
        children: [
        ],
      },
    ],
  },
  {
    title: 'pe-pages',
    lang: 'pe-pages',
    icon: 'box',
    link: '#',
    children: [
      {
        title: 'pe-account',
        link: '#',
        lang: 'pe-account',
        children: [
          {
            title: 'pe-account',
            lang: 'pe-account',
            link: '/pages-account-settings',
            children: [],
          },
          {
            title: 'pe-security',
            lang: 'pe-security',
            link: '/pages-account-security',
            children: [],
          },
          {
            title: 'pe-billing-plans',
            lang: 'pe-billing-plans',
            link: '/pages-account-billing-plan',
            children: [],
          },
          {
            title: 'pe-notification',
            lang: 'pe-notification',
            link: '/pages-account-notification',
            children: [],
          },
          {
            title: 'pe-statements',
            lang: 'pe-statements',
            link: '/pages-account-statements',
            children: [],
          },
          {
            title: 'pe-logs',
            lang: 'pe-logs',
            link: '/pages-account-logs',
            children: [],
          },
        ],
      },
      {
        title: 'pe-user-profile',
        link: '#',
        lang: 'pe-user-profile',
        children: [
          {
            title: 'pe-overview',
            lang: 'pe-overview',
            link: '/pages-user',
            children: [],
          },
          {
            title: 'pe-activity',
            lang: 'pe-activity',
            link: '/pages-user-activity',
            children: [],
          },
          {
            title: 'pe-followers',
            lang: 'pe-followers',
            link: '/pages-user-followers',
            children: [],
          },
          {
            title: 'pe-documents',
            lang: 'pe-documents',
            link: '/pages-user-documents',
            children: [],
          },
          {
            title: 'pe-notes',
            lang: 'pe-notes',
            link: '/pages-user-notes',
            children: [],
          },
          {
            title: 'pe-projects',
            lang: 'pe-projects',
            link: '/pages-user-projects',
            children: [],
          },
        ],
      },
      {
        title: 'pe-contact-us',
        lang: 'pe-contact-us',
        link: '/pages-contact-us',
        children: [],
      },
      {
        title: "pe-faqs",
        lang: 'pe-faqs',
        link: '/pages-faq',
        children: [],
      },
      {
        title: 'pe-licenses',
        lang: 'pe-licenses',
        link: '/pages-licenses',
        children: [],
      },
      {
        title: 'pe-coming-soon',
        lang: 'pe-coming-soon',
        link: '/pages-coming-soon',
        children: [],
      },
      {
        title: 'pe-maintenance',
        lang: 'pe-maintenance',
        link: '/pages-maintenance',
        children: [],
      },
      {
        title: 'pe-privacy-policy',
        lang: 'pe-privacy-policy',
        link: '/pages-privacy-policy',
        children: [],
      },
      {
        title: 'pe-help-center',
        lang: 'pe-help-center',
        link: '/pages-help-center',
        children: [],
      }
    ],
  },
  {
    title: 'pe-support',
    lang: 'pe-support',
    icon: 'life-buoy',
    link: 'https://1.envato.market/domiex-admin-dashboard-support',
    children: [],
  },
  {
    title: 'pe-documentation',
    lang: 'pe-documentation',
    icon: 'file-text',
    link: 'https://srbthemes.kcubeinfotech.com/domiex/docs/angular/index.html',
    children: [],
  },
  {
    title: 'pe-changelog',
    lang: 'pe-changelog',
    icon: 'feather',
    link: 'https://srbthemes.kcubeinfotech.com/domiex/live/changelog.html',
    children: [],
  },
  {
    title: 'pe-changelog',
    lang: 'pe-changelog',
    icon: 'feather',
    link: 'https://srbthemes.kcubeinfotech.com/domiex/live/changelog.html',
    children: [],
  },
];