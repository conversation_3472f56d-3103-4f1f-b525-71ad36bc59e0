<app-page-title [pageTitle]="'Customers'" [Title]="'List View'"></app-page-title>

<div>
  <div class="grid items-center grid-cols-12 gap-5 mb-5">
    <div class="col-span-12 xl:col-span-8">
      <h6 class="card-title">Customer List</h6>
    </div>
    <div class="flex flex-wrap justify-end col-span-12 gap-3 xl:col-span-4">
      <button class="btn btn-red btn-icon " *ngIf="checkedRows.length" (click)="deleteSelectedItem()">
        <lucide-angular name="Trash" class="inline-block size-4"></lucide-angular>
      </button>
      <div class="relative group/form">
        <input type="email"
          class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4"
          placeholder="Search name, email, gender, etc..." [(ngModel)]="searchText"
          (ngModelChange)="updateDisplayedData()">
        <span
          class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-none">
          <lucide-angular name="Search" class="size-4"></lucide-angular>
        </span>
      </div>
      <button class="btn btn-primary" (click)="openCreateModal(null ,null)">
        <lucide-angular name="Plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> New Customer
      </button>
    </div>
  </div>
  <div>
    <div class="overflow-x-auto">
      <table class="table border-separate hovered flush border-spacing-y-2 whitespace-nowrap">
        <tbody class="*:bg-gray-50 dark:*:bg-dark-900 *:rounded-md">
          <tr class="text-gray-500 bg-gray-100 dark:bg-dark-800 dark:text-dark-500">
            <th *ngFor="let col of columnDefs" class="!font-medium cursor-pointer">
              <div class="flex items-center gap-space">
                <div class="!font-medium" *ngIf="col.headerCheckbox">
                  <div class="input-check-group">
                    <label for="checkboxAll" class="hidden input-check-label"></label>
                    <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" [(ngModel)]="headerCheckBox" (change)="headerCheckBoxChange()" />
                  </div>
                </div>
                <div (click)="onSortChange(col)">
                  {{col.headerName}} <span *ngIf="col.sortable">
                    {{ col.sortDiraction === 'asc' ? '↑' : '↓' }}</span>
                </div>
              </div>
            </th>
          </tr>
          <ng-container *ngIf="displayedData.length > 0">
            <ng-container *ngFor="let product of displayedData; let i = index">
              <tr>
                <td>
                  <div class="flex items-center gap-space">
                    <div class="!font-medium" *ngIf="hasHeaderCheckbox">
                      <div class="input-check-group">
                        <label for="checkboxAll" class="hidden input-check-label"></label>
                        <input id="checkboxAll" class="input-check input-check-primary" type="checkbox"
                          x-model="selectAll" [checked]="product.checked" (change)="onRowCheckboxChange(i)"/>
                      </div>
                    </div>
                    <a href="javascript: void(0);" class="link link-primary">{{ product.customersID }}</a>
                  </div>
                </td>
                <td>
                  <div class="flex items-center gap-2">
                    <img [src]="product.image" alt="" class="rounded-full shrink-0 size-8">
                    <a href="javascript: void(0);" class="text-current link link-primary grow"
                      >{{product.customersName}}</a>
                  </div>
                </td>
                <td >{{product.email}}</td>
                <td >{{product.phoneNumber}}</td>
                <td >{{product.subscriber}}</td>
                <td >{{product.gender}}</td>
                <td >{{product.location}}</td>
                <td>
                  <span  class="badge" [ngClass]="{
                                          'badge-green': product.status === 'Active',
                                          'badge-red': product.status === 'Inactive'
                                      }">{{product.status}}</span>
                </td>
                <td>
                  <div class="dropdown">
                    <button x-ref="button" type="button" domixDropdownToggle [dropdownMenu]="dropdown2"
                      class="flex items-center text-gray-500 dark:text-dark-500">
                      <i class="ri-more-2-fill"></i>
                    </button>
                    <div #dropdown2 class="!fixed p-2 dropdown-menu">
                      <ul>
                        <li>
                          <a href="javascript: void(0);" data-modal-target="overviewCustomerModals"
                            class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i>
                            <span>Overview</span>
                          </a>
                        </li>
                        <li>
                          <a href="javascript: void(0);" data-modal-target="addCustomerModals"
                            (click)="openCreateModal(product,i)" class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i>
                            <span>Edit</span>
                          </a>
                        </li>
                        <li>
                          <a href="javascript: void(0);" data-modal-target="deleteModal" (click)="deleteModal(i)"
                            class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i>
                            <span>Delete</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
            </ng-container>
          </ng-container>
          <tr>
            <ng-container *ngIf="displayedData.length == 0">
              <td colspan="10" class="!p-8">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                  <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598"
                    gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#60e8fe"></stop>
                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                    <stop offset=".197" stop-color="#97f0fe"></stop>
                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                    <stop offset=".525" stop-color="#dafaff"></stop>
                    <stop offset=".687" stop-color="#eefdff"></stop>
                    <stop offset=".846" stop-color="#fbfeff"></stop>
                    <stop offset="1" stop-color="#fff"></stop>
                  </linearGradient>
                  <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)"
                    d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                  </path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                    stroke-miterlimit="10" stroke-width="3"
                    d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                  </path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                    stroke-miterlimit="10" stroke-width="3"
                    d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                  </path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                    stroke-miterlimit="10" stroke-width="3"
                    d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                  </path>
                </svg>
                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
              </td>
            </ng-container>
          </tr>
        </tbody>
      </table>
    </div>
    <app-domix-pagination [currentPage]="currentPage" [totalPages]="totalPages" [showingStart]="showingStart" [showingEnd]="showingEnd" [totalResults]="gridData.length" (pageChanged)="onPageChange($event)">
    </app-domix-pagination>
  </div>
