[{"teacherName": "<PERSON>", "image": "assets/images/avatar/user-1.png", "email": "<EMAIL>", "gross": "$70,000", "taxes": "$20,000", "netSalary": "$50,000", "performance": "Excellent", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-2.png", "email": "<EMAIL>", "gross": "$75,000", "taxes": "$22,000", "netSalary": "$53,000", "performance": "Good", "status": "Active"}, {"teacherName": "<PERSON>", "email": "micha<PERSON>@SRBThemes.com", "gross": "$80,000", "taxes": "$25,000", "netSalary": "$55,000", "performance": "Excellent", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-4.png", "email": "<EMAIL>", "gross": "$65,000", "taxes": "$18,000", "netSalary": "$47,000", "performance": "Good", "status": "Inactive"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-5.png", "email": "<EMAIL>", "gross": "$68,000", "taxes": "$19,000", "netSalary": "$49,000", "performance": "Satisfactory", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-6.png", "email": "<EMAIL>", "gross": "$73,000", "taxes": "$21,000", "netSalary": "$52,000", "performance": "Excellent", "status": "Active"}, {"teacherName": "<PERSON>", "email": "<EMAIL>", "gross": "$78,000", "taxes": "$23,000", "netSalary": "$55,000", "performance": "Good", "status": "Active"}, {"teacherName": "<PERSON>", "email": "and<PERSON>@SRBThemes.com", "gross": "$72,000", "taxes": "$20,000", "netSalary": "$52,000", "performance": "Satisfactory", "status": "Inactive"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-9.png", "email": "<EMAIL>", "gross": "$76,000", "taxes": "$22,000", "netSalary": "$54,000", "performance": "Excellent", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-10.png", "email": "<EMAIL>", "gross": "$70,000", "taxes": "$20,000", "netSalary": "$50,000", "performance": "Good", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-11.png", "email": "<EMAIL>", "gross": "$69,000", "taxes": "$19,000", "netSalary": "$50,000", "performance": "Satisfactory", "status": "Inactive"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-12.png", "email": "<EMAIL>", "gross": "$74,000", "taxes": "$21,000", "netSalary": "$53,000", "performance": "Excellent", "status": "Active"}, {"teacherName": "<PERSON>", "email": "<EMAIL>", "gross": "$79,000", "taxes": "$24,000", "netSalary": "$55,000", "performance": "Good", "status": "Active"}, {"teacherName": "<PERSON>", "image": "assets/images/avatar/user-14.png", "email": "<EMAIL>", "gross": "$71,000", "taxes": "$20,000", "netSalary": "$51,000", "performance": "Satisfactory", "status": "Inactive"}]