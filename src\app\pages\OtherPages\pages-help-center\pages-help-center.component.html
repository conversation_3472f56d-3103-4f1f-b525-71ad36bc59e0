<app-page-title [Title]="'Licenses'" [pageTitle]="'Pages'"></app-page-title>

<div class="grid grid-cols-12 gap-x-space">

  <div class="col-span-12 xl:col-span-4 2xl:col-span-3">
    <div class="card">
      <div class="card-body">
        <h6>Search for a Question</h6>
        <p class="mb-3 text-gray-500 dark:text-dark-500">Type your question or search keyword</p>
        <div class="relative group/form">
          <input type="text" id="iconWithInput"
            class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4"
            placeholder="Start typing ..." [formControl]="searchControl">
          <button
            class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-none">
            <lucide-angular name="search" class="size-4">
            </lucide-angular>
          </button>
        </div>
        <div class="h-auto lg:h-[calc(100vh_-_28rem)]" data-simplebar>
          <ul class="px-1 my-5 space-y-3">
            <li><a href="javascript: void(0);" [ngClass]="selectedCatogary === Catgary.GettingStarted ? 'active' : ''"
                (click)="setSelectedCatogry( Catgary.GettingStarted) "
                class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm [&.active]:outline outline-offset-2 [&.active]:outline-primary-500/20 ">Getting
                Started <span class="py-1 leading-none align-middle badge badge-primary ltr:ml-1 rtl:mr-1">5</span></a>
            </li>
            <li><a href="javascript: void(0);" [ngClass]="selectedCatogary === Catgary.AccountwithCard ? 'active' : ''"
                (click)="setSelectedCatogry( Catgary.AccountwithCard)"
                class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm [&.active]:outline outline-offset-2 [&.active]:outline-primary-500/20">Account
                with Card <span
                  class="py-1 leading-none align-middle badge badge-primary ltr:ml-1 rtl:mr-1">3</span></a>
            </li>
            <li><a href="javascript: void(0);" [ngClass]="selectedCatogary === Catgary.LicensesPolicy ? 'active' : ''"
                (click)="setSelectedCatogry(Catgary.LicensesPolicy)"
                class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm [&.active]:outline outline-offset-2 [&.active]:outline-primary-500/20">Licenses
                Policy</a></li>
            <li><a href="javascript: void(0);"
                [ngClass]="selectedCatogary === Catgary.CustomizeTemplates ? 'active' : ''" (click)="setSelectedCatogry(Catgary.CustomizeTemplates)
              "
                class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm [&.active]:outline outline-offset-2 [&.active]:outline-primary-500/20">Customize
                Templates</a></li>
            <li><a href="javascript: void(0);" [ngClass]="selectedCatogary=== Catgary.CustomizeLayouts ? 'active' : ''"
                (click)="setSelectedCatogry(Catgary.CustomizeLayouts)"
                class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm [&.active]:outline outline-offset-2 [&.active]:outline-primary-500/20">Customize
                Layouts</a></li>
          </ul>
        </div>

        <div class="relative px-4 py-3 overflow-hidden rounded-md bg-primary-600">
          <div class="absolute bottom-0 ltr:right-0 rtl:left-0">
            <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"
              xmlns:svgjs="http://svgjs.dev/svgjs" width="300" height="160" preserveAspectRatio="none"
              viewBox="0 0 300 160">
              <g mask="url(&quot;#SvgjsMask1016&quot;)" fill="none">
                <path d="M161.32 191.44C190.36 168.14 173.39 56.82 222.5 55.64 271.61 54.46 310.21 119.21 344.86 121.24"
                  class="stroke-primary-400/15" stroke-width="2"></path>
                <path d="M103.48 160.94C139.34 155.85 149.02 78.76 217.61 71.68 286.2 64.6 298.91 8.36 331.74 6.08"
                  class="stroke-primary-400/15" stroke-width="2"></path>
                <path d="M118.76 180.58C146.95 179.59 162.2 139.63 222.03 135.17 281.86 130.71 293.38 62.42 325.3 58.37"
                  class="stroke-primary-400/15" stroke-width="2"></path>
                <path
                  d="M96.26 168.19C127.98 167.05 145.97 121.03 212.32 116.71 278.68 112.39 293.56 41.87 328.39 38.31"
                  class="stroke-primary-400/15" stroke-width="2"></path>
                <path
                  d="M50.02 170.02C76.2 169.48 99.14 134.5 148.57 134.49 198 134.48 197.84 154.49 247.12 154.49 296.39 154.49 320.53 134.59 345.67 134.49"
                  class="stroke-primary-400/15" stroke-width="2"></path>
              </g>
              <defs>
                <mask id="SvgjsMask1016">
                  <rect width="300" height="160" fill="#ffffff"></rect>
                </mask>
              </defs>
            </svg>
          </div>
          <h6 class="mb-4 text-primary-50">Do you still need our help ?</h6>
          <button data-modal-target="" (click)="openContactModal()"
            class="relative text-white group/effect bg-primary-500 border-primary-500 hover:bg-primary-600 hover:text-white hover:border-primary-600 focus:bg-primary-600 focus:text-white focus:border-primary-600 btn">
            <span class="absolute inset-0 overflow-hidden rounded-xl">
              <span
                class="absolute inset-0 rounded-xl bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(56,189,248,0.6)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover/effect:opacity-100">
              </span>
            </span>
            <span>
              Contact Us
              <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
              <lucide-angular name="MoveLeft" class="mr-1 ltr:hidden rtl:inline-block size-4"></lucide-angular>
            </span>
          </button>
        </div>
      </div>
    </div>

  </div>

  <!-- <app-right *ngIf="filteredData && filteredData.length > 0" [data]="filteredData"></app-right> -->

  <div class="col-span-12 xl:col-span-8 2xl:col-span-9">
    <div class="list">
      <div class="flex flex-wrap items-center gap-3 mb-5">
        <ul class="overflow-x-auto tabs grow">
          <li><a href="javascript:void(0)" (click)="setSelectedCatogryStatus( catogaryStatus.AllTicket)"
              [ngClass]="setSelectedFiltered === catogaryStatus.AllTicket ? 'active' : ''"
              class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">All
              Tickets</a></li>
          <li><a href="javascript:void(0)" (click)="setSelectedCatogryStatus( catogaryStatus.Active)"
              [ngClass]="setSelectedFiltered === catogaryStatus.Active ? 'active' : ''"
              class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Active</a>
          </li>
          <li><a href="javascript:void(0)" (click)="setSelectedCatogryStatus( catogaryStatus.Closed)"
              [ngClass]="setSelectedFiltered === catogaryStatus.Closed ? 'active' : ''"
              class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Closed</a>
          </li>
          <li><a href="javascript:void(0)" (click)="setSelectedCatogryStatus( catogaryStatus.Deleted)"
              [ngClass]="setSelectedFiltered === catogaryStatus.Deleted ? 'active' : ''"
              class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Deleted</a>
          </li>
        </ul>
        <div class="shrink-0">
          <button class="btn btn-sky btn-icon-overlay" (click)="openNewTicketsModal()" data-modal-target=""><span
              class="icon"><i class="size-5 ri-pencil-line"></i></span>New
            Tickets</button>
        </div>
      </div>

      <div class="flex flex-col gap-3">
        <ng-container *ngIf="filteredTickets.length">
          <ngx-simplebar [options]="options" class="h-[calc(100vh_-_16.8rem)]">
            <div class="card !mb-0" *ngFor="let ticket of filteredTickets">
              <div class="card-body">
                <div class="flex items-center gap-5 mb-4">
                  <h6 class="underline grow"><a href="javascript: void(0);">Ticket #<span></span></a></h6>
                  <div class="flex items-center gap-4 shrink-0">
                    <p class="text-sm text-gray-500 dark:text-dark-500"></p>
                    <div class="dropdown" domixDropdownToggle [dropdownMenu]="dropdown">
                      <button title="dropdown-button" type="button" class="flex items-center gap-2 p-0 btn">
                        <i class="ri-more-2-fill"></i>
                      </button>
                      <div class="p-2 dropdown-menu dropdown-right" #dropdown>
                        <a href="javascript: void(0);" class="dropdown-item">Reply
                          Task</a>
                        <a href="javascript: void(0);" class="dropdown-item">More
                          Details</a>
                      </div>
                    </div>
                  </div>
                </div>
                <h6 class="mb-1"><a href="javascript: void(0);">{{ticket.title}} </a>
                </h6>
                <p class="text-gray-500 dark:text-dark-500 line-clamp-2">
                  {{ticket.description}}
                </p>
                <div class="flex flex-wrap items-center gap-4 mt-5">
                  <div class="flex items-center gap-2 grow">
                    <img [src]="ticket.avatar" alt="" class="rounded-full size-8 shrink-0">
                    <h6>{{ticket.author}}</h6>
                  </div>
                  <div class="shrink-0">
                    <ng-container *ngFor="let tag of ticket.tags">
                      <a href="javascript: void(0);"
                        class="p-1 text-gray-500 transition duration-200 ease-linear dark:text-dark-500 hover:text-primary-500 dark:hover:text-primary-500"><span>{{tag}}
                        </span></a>
                    </ng-container>
                  </div>
                  <div class="shrink-0">
                    <a href="javascript: void(0);"
                      class="text-gray-500 transition duration-200 ease-linear dark:text-dark-500 hover:text-primary-500 dark:hover:text-primary-500"><i
                        class="inline-block align-middle size-5 ri-chat-3-line"></i> <span>
                        {{ticket.comments}}</span></a>
                  </div>
                </div>
              </div>
            </div>
          </ngx-simplebar>
        </ng-container>
        <ng-container *ngIf="filteredTickets.length == 0">
          <td colspan="10" class="!p-8">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
              <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598"
                gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#60e8fe"></stop>
                <stop offset=".033" stop-color="#6ae9fe"></stop>
                <stop offset=".197" stop-color="#97f0fe"></stop>
                <stop offset=".362" stop-color="#bdf5ff"></stop>
                <stop offset=".525" stop-color="#dafaff"></stop>
                <stop offset=".687" stop-color="#eefdff"></stop>
                <stop offset=".846" stop-color="#fbfeff"></stop>
                <stop offset="1" stop-color="#fff"></stop>
              </linearGradient>
              <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)"
                d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
              </path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"
                stroke-width="3"
                d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
              </path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"
                stroke-width="3"
                d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
              </path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"
                stroke-width="3"
                d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
              </path>
            </svg>
            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
          </td>
        </ng-container>
      </div>
    </div>
  </div>

</div>
