import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Invoicedata } from '../components/apps-invoice-create/apps-invoice-create.component';

@Injectable({
  providedIn: 'root',
})
export class InvoicedatasService {
  private Invoicedata: Invoicedata[] = [
    {
      invoicesID: 'INV-1001',
      image: '/assets/images/avatar/user-1.png',
      clientName: '<PERSON>',
      content: 'Consulting services for project X',
      country: 'USA',
      invoiceDate: '2024-09-15',
      dueDate: '2024-10-15',
      amount: '5000',
      email: '<EMAIL>',
      compnanyName: 'Google',
      phoneNumber: '789456122',
      status: 'Pending',
      productDetails: [
        {
          productName: 'Consulting Service',
          category: 'Services',
          qty: 5,
          unitPrice: 800,
          discount: 10,
          totalAmount: 3600,
        },
        {
          productName: 'Software License',
          category: 'Software',
          qty: 2,
          unitPrice: 1000,
          discount: 5,
          totalAmount: 1900,
        },
      ],
    },
    {
      invoicesID: 'INV-1002',
      image: 'assets/images/avatar/user-7.png',
      clientName: 'Rudn kos',
      content: 'Front End Development',
      country: 'New York',
      invoiceDate: '2024-05-18',
      dueDate: '2024-05-23',
      amount: '154.98',
      email: '<EMAIL>',
      compnanyName: 'Tech Solutions',
      phoneNumber: '123456789',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Frontend Consulting',
          category: 'Services',
          qty: 2,
          unitPrice: 75,
          discount: 0,
          totalAmount: 150,
        },
        {
          productName: 'Website Hosting',
          category: 'Hosting',
          qty: 1,
          unitPrice: 4.98,
          discount: 0,
          totalAmount: 4.98,
        },
      ],
    },
    {
      invoicesID: 'INV-1003',
      image: 'assets/images/avatar/user-2.png',
      clientName: 'Jane Smith',
      content: 'Laravel Development',
      country: 'California',
      invoiceDate: '2024-05-19',
      dueDate: '2024-05-24',
      amount: '200.00',
      email: '<EMAIL>',
      compnanyName: 'Web Dev LLC',
      phoneNumber: '987654321',
      status: 'Unpaid',
      productDetails: [
        {
          productName: 'Laravel API Development',
          category: 'Software',
          qty: 1,
          unitPrice: 200,
          discount: 0,
          totalAmount: 200,
        },
      ],
    },
    {
      invoicesID: 'INV-1004',
      image: 'assets/images/avatar/user-3.png',
      clientName: 'Alice Johnson',
      content: 'Web Designing',
      country: 'Texas',
      invoiceDate: '2024-05-20',
      dueDate: '2024-05-25',
      amount: '350.75',
      email: '<EMAIL>',
      compnanyName: 'Creative Design Co.',
      phoneNumber: '789123456',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Website Redesign',
          category: 'Design',
          qty: 3,
          unitPrice: 100,
          discount: 5,
          totalAmount: 285,
        },
        {
          productName: 'Graphic Design',
          category: 'Design',
          qty: 1,
          unitPrice: 65.75,
          discount: 0,
          totalAmount: 65.75,
        },
      ],
    },
    {
      invoicesID: 'INV-1005',
      image: 'assets/images/avatar/user-4.png',
      clientName: 'Bob Brown',
      content: 'Crop top Sweater Clothing',
      country: 'Florida',
      invoiceDate: '2024-05-21',
      dueDate: '2024-05-26',
      amount: '410.00',
      email: '<EMAIL>',
      compnanyName: 'Fashion World',
      phoneNumber: '654321987',
      status: 'Overdue',
      productDetails: [
        {
          productName: 'Custom Sweater Design',
          category: 'Apparel',
          qty: 4,
          unitPrice: 100,
          discount: 0,
          totalAmount: 400,
        },
        {
          productName: 'Shipping',
          category: 'Logistics',
          qty: 1,
          unitPrice: 10,
          discount: 0,
          totalAmount: 10,
        },
      ],
    },
    {
      invoicesID: 'INV-1006',
      image: 'assets/images/avatar/user-5.png',
      clientName: 'Charlie Davis',
      content: 'Bra Lace Crop top',
      country: 'Illinois',
      invoiceDate: '2024-05-22',
      dueDate: '2024-05-27',
      amount: '100.25',
      email: '<EMAIL>',
      compnanyName: 'Lingerie Boutique',
      phoneNumber: '321654987',
      status: 'Unpaid',
      productDetails: [
        {
          productName: 'Lace Bra',
          category: 'Apparel',
          qty: 2,
          unitPrice: 50,
          discount: 0,
          totalAmount: 100,
        },
        {
          productName: 'Handling Fee',
          category: 'Logistics',
          qty: 1,
          unitPrice: 0.25,
          discount: 0,
          totalAmount: 0.25,
        },
      ],
    },
    {
      invoicesID: 'INV-1007',
      image: 'assets/images/avatar/user-6.png',
      clientName: 'Emily Clark',
      content: 'Asp.Net Services',
      country: 'Ohio',
      invoiceDate: '2024-05-23',
      dueDate: '2024-05-28',
      amount: '250.50',
      email: '<EMAIL>',
      compnanyName: 'Aspire Technologies',
      phoneNumber: '111222333',
      status: 'Pending',
      productDetails: [
        {
          productName: '.NET Development',
          category: 'Software',
          qty: 1,
          unitPrice: 250.5,
          discount: 0,
          totalAmount: 250.5,
        },
      ],
    },
    {
      invoicesID: 'INV-1008',
      image: 'assets/images/avatar/user-7.png',
      clientName: 'David Martinez',
      content: 'Yellow women shoes',
      country: 'Michigan',
      invoiceDate: '2024-05-24',
      dueDate: '2024-05-29',
      amount: '300.00',
      email: '<EMAIL>',
      compnanyName: 'Fashion Forward',
      phoneNumber: '222333444',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Women Shoes - Yellow',
          category: 'Apparel',
          qty: 3,
          unitPrice: 100,
          discount: 0,
          totalAmount: 300,
        },
      ],
    },
    {
      invoicesID: 'INV-1009',
      image: 'assets/images/avatar/user-8.png',
      clientName: 'Sophia Garcia',
      content: 'Tote bag Leather Handbag Dolce',
      country: 'Georgia',
      invoiceDate: '2024-05-25',
      dueDate: '2024-05-30',
      amount: '120.75',
      email: '<EMAIL>',
      compnanyName: 'Luxury Bags Ltd',
      phoneNumber: '333444555',
      status: 'Pending',
      productDetails: [
        {
          productName: 'Leather Handbag',
          category: 'Apparel',
          qty: 1,
          unitPrice: 120.75,
          discount: 0,
          totalAmount: 120.75,
        },
      ],
    },
    {
      invoicesID: 'INV-1010',
      image: 'assets/images/avatar/user-9.png',
      clientName: 'Liam Miller',
      content: 'Chat Application',
      country: 'North Carolina',
      invoiceDate: '2024-05-26',
      dueDate: '2024-05-31',
      amount: '500.50',
      email: '<EMAIL>',
      compnanyName: 'Code Innovators',
      phoneNumber: '444555666',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Chat App Development',
          category: 'Software',
          qty: 1,
          unitPrice: 500.5,
          discount: 0,
          totalAmount: 500.5,
        },
      ],
    },
    {
      invoicesID: 'INV-1011',
      image: 'assets/images/avatar/user-10.png',
      clientName: 'Olivia Wilson',
      content: 'Laravel Development',
      country: 'New Jersey',
      invoiceDate: '2024-05-27',
      dueDate: '2024-06-01',
      amount: '450.00',
      email: '<EMAIL>',
      compnanyName: 'Wilson Tech',
      phoneNumber: '555666777',
      status: 'Unpaid',
      productDetails: [
        {
          productName: 'Laravel API Development',
          category: 'Software',
          qty: 1,
          unitPrice: 450,
          discount: 0,
          totalAmount: 450,
        },
      ],
    },
    {
      invoicesID: 'INV-1012',
      image: 'assets/images/avatar/user-11.png',
      clientName: 'Mason Anderson',
      content: 'Straw hat Cap Cowboy hat Sun hat',
      country: 'Virginia',
      invoiceDate: '2024-05-28',
      dueDate: '2024-06-02',
      amount: '330.25',
      email: '<EMAIL>',
      compnanyName: 'Hat World',
      phoneNumber: '666777888',
      status: 'Pending',
      productDetails: [
        {
          productName: 'Cowboy Hat',
          category: 'Apparel',
          qty: 2,
          unitPrice: 150,
          discount: 0,
          totalAmount: 300,
        },
        {
          productName: 'Shipping',
          category: 'Logistics',
          qty: 1,
          unitPrice: 30.25,
          discount: 0,
          totalAmount: 30.25,
        },
      ],
    },

    {
      invoicesID: 'INV-1013',
      image: 'assets/images/avatar/user-3.png',
      clientName: 'Alice Johnson',
      content: 'Web Designing',
      country: 'Texas',
      invoiceDate: '2024-05-20',
      dueDate: '2024-05-25',
      amount: '350.75',
      email: '<EMAIL>',
      compnanyName: 'Creative Design Co.',
      phoneNumber: '789123456',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Website Redesign',
          category: 'Design',
          qty: 3,
          unitPrice: 100,
          discount: 5,
          totalAmount: 285,
        },
        {
          productName: 'Graphic Design',
          category: 'Design',
          qty: 1,
          unitPrice: 65.75,
          discount: 0,
          totalAmount: 65.75,
        },
      ],
    },
    {
      invoicesID: 'INV-1014',
      image: 'assets/images/avatar/user-4.png',
      clientName: 'Bob Brown',
      content: 'Crop top Sweater Clothing',
      country: 'Florida',
      invoiceDate: '2024-05-21',
      dueDate: '2024-05-26',
      amount: '410.00',
      email: '<EMAIL>',
      compnanyName: 'Fashion World',
      phoneNumber: '654321987',
      status: 'Overdue',
      productDetails: [
        {
          productName: 'Custom Sweater Design',
          category: 'Apparel',
          qty: 4,
          unitPrice: 100,
          discount: 0,
          totalAmount: 400,
        },
        {
          productName: 'Shipping',
          category: 'Logistics',
          qty: 1,
          unitPrice: 10,
          discount: 0,
          totalAmount: 10,
        },
      ],
    },
    {
      invoicesID: 'INV-1015',
      image: 'assets/images/avatar/user-5.png',
      clientName: 'Charlie Davis',
      content: 'Bra Lace Crop top',
      country: 'Illinois',
      invoiceDate: '2024-05-22',
      dueDate: '2024-05-27',
      amount: '100.25',
      email: '<EMAIL>',
      compnanyName: 'Lingerie Boutique',
      phoneNumber: '321654987',
      status: 'Unpaid',
      productDetails: [
        {
          productName: 'Lace Bra',
          category: 'Apparel',
          qty: 2,
          unitPrice: 50,
          discount: 0,
          totalAmount: 100,
        },
        {
          productName: 'Handling Fee',
          category: 'Logistics',
          qty: 1,
          unitPrice: 0.25,
          discount: 0,
          totalAmount: 0.25,
        },
      ],
    },
    {
      invoicesID: 'INV-1016',
      image: 'assets/images/avatar/user-6.png',
      clientName: 'Emily Clark',
      content: 'Asp.Net Services',
      country: 'Ohio',
      invoiceDate: '2024-05-23',
      dueDate: '2024-05-28',
      amount: '250.50',
      email: '<EMAIL>',
      compnanyName: 'Aspire Technologies',
      phoneNumber: '111222333',
      status: 'Pending',
      productDetails: [
        {
          productName: '.NET Development',
          category: 'Software',
          qty: 1,
          unitPrice: 250.5,
          discount: 0,
          totalAmount: 250.5,
        },
      ],
    },
    {
      invoicesID: 'INV-1017',
      image: 'assets/images/avatar/user-7.png',
      clientName: 'David Martinez',
      content: 'Yellow women shoes',
      country: 'Michigan',
      invoiceDate: '2024-05-24',
      dueDate: '2024-05-29',
      amount: '300.00',
      email: '<EMAIL>',
      compnanyName: 'Fashion Forward',
      phoneNumber: '222333444',
      status: 'Paid',
      productDetails: [
        {
          productName: 'Women Shoes - Yellow',
          category: 'Apparel',
          qty: 3,
          unitPrice: 100,
          discount: 0,
          totalAmount: 300,
        },
      ],
    },
  ];

  constructor() { }

  getInvoicedata(): Observable<Invoicedata[]> {
    return of(this.Invoicedata);
  }

  addInvoicedata(Invoicedata: Invoicedata): Observable<Invoicedata> {
    if (Array.isArray(this.Invoicedata)) {
      this.Invoicedata = [Invoicedata, ...this.Invoicedata];
    }
    return of(Invoicedata);
  }

  updateInvoicedata(updatedInvoicedata: Invoicedata): Observable<Invoicedata> {
    const updatedInvoicedatas = this.Invoicedata.map((Invoicedata) =>
      Invoicedata.invoicesID === updatedInvoicedata.invoicesID
        ? updatedInvoicedata
        : Invoicedata
    );

    this.Invoicedata = [...updatedInvoicedatas];

    return of(updatedInvoicedata);
  }

  deleteInvoicedata(id: string): Observable<void> {
    this.Invoicedata = this.Invoicedata.filter((s) => s.invoicesID !== id);
    return of(void 0);
  }
}
