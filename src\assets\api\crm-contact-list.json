[{"image": "assets/images/avatar/user-1.png", "contactName": "<PERSON>", "phoneNumber": "+890 1829 15781", "company": "StarTech Dynamics", "role": "Web Designer", "email": "<EMAIL>", "website": "patmartizen.com", "status": "Customer"}, {"image": "assets/images/avatar/user-2.png", "contactName": "<PERSON>", "phoneNumber": "+957 6326 78821", "company": "StarTech Dynamics", "role": "Web Designer", "email": "<EMAIL>", "website": "janebrown.com", "status": "Personal"}, {"image": "assets/images/avatar/user-3.png", "contactName": "<PERSON>", "phoneNumber": "+264 1427 33002", "company": "StarTech Dynamics", "role": "UI / UX Designer", "email": "<EMAIL>", "website": "johndavis.com", "status": "Customer"}, {"image": "assets/images/avatar/user-4.png", "contactName": "<PERSON>", "phoneNumber": "+688 9444 65363", "company": "BrightFuture Tech", "role": "Graphic Designer", "email": "<EMAIL>", "website": "jordandavis.com", "status": "Customer"}, {"image": "assets/images/avatar/user-5.png", "contactName": "<PERSON>", "phoneNumber": "+300 8108 69119", "company": "Quantum Innovations", "role": "Co Founder", "email": "<EMAIL>", "website": "alexlee.com", "status": "Personal"}, {"image": "assets/images/avatar/user-6.png", "contactName": "<PERSON>", "phoneNumber": "+646 9347 84543", "company": "BrightFuture Tech", "role": "Graphic Designer", "email": "<EMAIL>", "website": "casetmartinez.com", "status": "Personal"}, {"image": "assets/images/avatar/user-7.png", "contactName": "<PERSON>", "phoneNumber": "+749 6102 50325", "company": "BrightFuture Tech", "role": "ASP.Net Developer", "email": "<EMAIL>", "website": "taylor.com", "status": "Customer"}, {"image": "assets/images/avatar/user-8.png", "contactName": "<PERSON>", "phoneNumber": "+829 5728 93265", "company": "BlueSky Enterprises", "role": "Product Manager", "email": "<EMAIL>", "website": "chrissmith.com", "status": "Employee"}, {"image": "assets/images/avatar/user-9.png", "contactName": "<PERSON>", "phoneNumber": "+213 9689 10505", "company": "BrightFuture Tech", "role": "web Developer", "email": "<EMAIL>", "website": "jane.brigth.com", "status": "Personal"}, {"image": "assets/images/avatar/user-10.png", "contactName": "<PERSON>", "phoneNumber": "+846 9274 23870", "company": "Quantum Innovations", "role": "Product Manager", "email": "<EMAIL>", "website": "johangarcia.com", "status": "Personal"}, {"image": "assets/images/avatar/user-11.png", "contactName": "<PERSON>", "phoneNumber": "+285 1994 96029", "company": "BlueSky Enterprises", "role": "React Developer", "email": "<EMAIL>", "website": "chriswilson.com", "status": "Customer"}, {"image": "assets/images/avatar/user-12.png", "contactName": "<PERSON>", "phoneNumber": "+695 2025 51582", "company": "BrightFuture Tech", "role": "Software Engineer", "email": "<EMAIL>", "website": "alextech.com", "status": "Customer"}, {"image": "assets/images/avatar/user-13.png", "contactName": "<PERSON>", "phoneNumber": "+840 4447 94334", "company": "BlueSky Enterprises", "role": "<PERSON><PERSON>", "email": "<EMAIL>", "website": "cameron.com", "status": "Customer"}, {"image": "assets/images/avatar/user-14.png", "contactName": "<PERSON>", "phoneNumber": "+438 6305 33828", "company": "BlueSky Enterprises", "role": "Marketing Specialist", "email": "<EMAIL>", "website": "sambrown.com", "status": "Marketing"}, {"image": "assets/images/avatar/user-15.png", "contactName": "<PERSON>", "phoneNumber": "+356 8229 92921", "company": "Synergy Solutions", "role": "<PERSON><PERSON>", "email": "<EMAIL>", "website": "patmartiz.com", "status": "Employee"}, {"image": "assets/images/avatar/user-16.png", "contactName": "<PERSON>", "phoneNumber": "+880 8152 56315", "company": "BrightFuture Tech", "role": "UI / UX Designer", "email": "<EMAIL>", "website": "chrissmith.com", "status": "Personal"}, {"image": "assets/images/avatar/user-17.png", "contactName": "<PERSON>", "phoneNumber": "+599 4447 23760", "company": "BlueSky Enterprises", "role": "Co Founder", "email": "<EMAIL>", "website": "cameronwilson.com", "status": "Employee"}, {"image": "assets/images/avatar/user-18.png", "contactName": "<PERSON>", "phoneNumber": "+590 5863 84911", "company": "StarPath Dynamics", "role": "UI / UX Designer", "email": "<EMAIL>", "website": "caseystarpath.com", "status": "Customer"}]