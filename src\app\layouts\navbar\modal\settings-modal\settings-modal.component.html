<div id="settingsModal" class="modal">
  <div class="modal-wrap modal-center modal-xl">
    <div class="modal-header">
      <h6>Domiex Customize</h6>
      <button (click)="closeSettingsModal()">
        <lucide-angular name="x" class="size-5"></lucide-angular>
      </button>
    </div>
    <div class="modal-content">
      <div>
        <h6 class="mb-3">Select Layout:</h6>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-space">
          <ng-container *ngFor="let layout of layouts">
            <ng-container *ngIf="layout.value !== 'default' && layout.value !== 'horizontal' else otherLayout">
              <div class="flex-col hidden gap-0 input-radio-group lg:flex">
                <input (change)="setLayout(layout.value)" [id]="layout.value + 'Layout'" name="layout" type="radio" [checked]="settings.layout === layout.value" class="hidden input-radio peer">
                <label [for]="layout.value + 'Layout'" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                  <span class="flex h-full">
                    <span class="w-3 h-full shrink-0 bg-gray-50 dark:bg-dark-850"></span>
                    <span class="grow">
                      <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                        <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                        <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                        <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                      </span>
                      <span class="p-1.5 block">
                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      </span>
                    </span>
                  </span>
                </label>
                <label [for]="layout.value + 'Layout'" class="cursor-pointer form-label">{{layout.text}}</label>
              </div>
            </ng-container>
            <ng-template #otherLayout>
              <div class="flex-col gap-0 input-radio-group">
                <input (change)="setLayout(layout.value)" [id]="layout.value + 'Layout'" name="layout" type="radio" [checked]="settings.layout === layout.value" class="hidden input-radio peer">
                <label [for]="layout.value + 'Layout'" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                  <span class="block h-full">
                    <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                      <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                      <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                      <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                    </span>
                    <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                      <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                      <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      </span>
                    </span>
                  </span>
                </label>
                <label [for]="layout.value + 'Layout'" class="cursor-pointer form-label">{{layout.text}}</label>
              </div>
            </ng-template>
          </ng-container>
        </div>

        <ng-container *ngIf="settings.layout === 'modern'">
          <div class="hidden lg:block">
            <h6 class="my-3">Navigation Type</h6>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-space">
              <div class="input-radio-group" *ngFor="let nav of navigationType">
                <input (change)="setNavigation(nav.value)" [id]="nav.value + 'Type'" name="navType" type="radio" [checked]="settings.navigationType === nav.value" class="input-radio input-radio-primary">
                <label [for]="nav.value + 'Type'" class="input-radio-label">{{nav.text}}</label>
              </div>
            </div>
          </div>
        </ng-container>


        <div class="hidden xl:block">
          <h6 class="my-4">Content Width:</h6>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
            <div class="flex-col gap-0 input-radio-group" *ngFor="let width of containerWidth;let i=index">
              <input (change)="setContentWidth(width.value)" [id]="'content_' + i" name="contentWidth" type="radio" [checked]="settings.contentWidth === width.value" class="hidden input-radio peer" />
              <label [for]="'content_' + i" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                <span class="block h-full">
                  <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                  </span>
                  <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                    <span class="h-[calc(100%_-_8px)] col-span-10 py-1.5 px-4 inline-block">
                      <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    </span>
                  </span>
                </span>
              </label>
              <label [for]="'content_' + i"class="cursor-pointer form-label">{{width.text}}</label>
            </div>
          </div>
        </div>
    
        <ng-container *ngIf="settings.layout !== 'horizontal'" >
          <div class="hidden lg:block">
            <h6 class="my-4">Sidebar Size:</h6>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-space">
              <div class="flex-col gap-0 input-radio-group" *ngFor="let type of sidebarType;let i=index">
                <input [id]="'defaultSidebar_' + i" (change)="setSidebar(type.value)" name="sidebar" type="radio" [checked]="settings.sidebarSize === type.value" class="hidden input-radio peer">
                <label [for]="'defaultSidebar_' + i" class="block w-full h-24 mb-3 overflow-hidden cursor-pointer card peer-checked:border-primary-500">
                  <span class="block h-full">
                    <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                      <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                      <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                      <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                    </span>
                    <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                      <span class="h-[calc(100%_-_8px)] col-span-3 bg-gray-50 dark:bg-dark-850"></span>
                      <span class="h-[calc(100%_-_8px)] col-span-9 py-1.5 px-4 inline-block">
                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                      </span>
                    </span>
                  </span>
                </label>
                <label for="defaultSidebar" class="cursor-pointer form-label">{{type.text}}</label>
              </div>
            </div>
          </div>
        </ng-container>

        <h6 class="my-4">Layout Direction:</h6>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
          <div class="flex-col gap-0 input-radio-group" *ngFor="let dir of direction; let i = index">
            <input (change)="setDir(dir.value)" [id]="'ltrMode_' + i" name="layoutDirection" type="radio" [checked]="settings.direction === dir.value" class="hidden input-radio peer" />
            <label [for]="'ltrMode_' + i" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
              <span class="block h-full">
                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 py-1.5 px-4 inline-block">
                    <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                  </span>
                </span>
              </span>
            </label>
            <label [for]="'ltrMode_' + i" class="cursor-pointer form-label">{{dir.text}}</label>
          </div>
        </div>

        <h6 class="my-4">Layout Mode:</h6>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
          <div class="flex-col gap-0 input-radio-group" *ngFor="let mode of modes; let i = index">
            <input (change)="setLayoutMode(mode.value)" [id]="'lightMode_' + i" name="layoutMode" type="radio" [checked]="settings.mode === mode.value" class="hidden input-radio peer" />
            <label [for]="'lightMode_' + i" class="{{mode.class}}">
              <span class="block h-full" *ngIf="mode.value === 'light'">
                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                    <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                  </span>
                </span>
              </span>
              <span class="block h-full" *ngIf="mode.value === 'dark'">
                <span class="flex gap-1 px-4 py-1.5 bg-dark-700/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-dark-900 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                    <span class="block w-1/3 h-1.5 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-full h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                  </span>
                </span>
              </span>
              <span class="relative block h-full" *ngIf="mode.value === 'auto'">
                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                    <span class="block w-1/3 h-1.5 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-full h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                  </span>
                </span>
              </span>
              <span class="block h-full" *ngIf="mode.value === 'black-white'">
                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                    <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                  </span>
                </span>
              </span>
            </label>
            <label [for]="'lightMode_' + i" class="cursor-pointer form-label">{{mode.text}}</label>
          </div>
          <!-- <div class="flex-col gap-0 input-radio-group">
            <input @change="setLayoutMode('auto')" id="autoMode" name="layoutMode" type="radio" x-model="curLayoutMode" value="auto" class="hidden input-radio peer">
            <label for="autoMode" class="relative block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500 before:absolute before:bg-gray-950 before:w-1/2 before:inset-y-0 before:right-0">
              <span class="relative block h-full">
                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                  <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                  <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                  <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                </span>
                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                  <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                  <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                    <span class="block w-1/3 h-1.5 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/2 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-full h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-1/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                    <span class="block w-2/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                  </span>
                </span>
              </span>
            </label>
            <label for="autoMode" class="cursor-pointer form-label">Default System</label>
          </div> -->
        </div>

        <ng-container *ngIf="settings.mode === 'dark'">
          <div>
            <h6 class="my-4 darkModeColors">Dark Mode Colors:</h6>
            <div class="flex flex-wrap items-center gap-3 darkModeColors">
              <div class="input-radio-group" *ngFor="let dColor of darkModeColor; let i = index">
                <input (change)="setDarkModeColors(dColor.value)" [id]="'noneColors_' + i"
                name="darkModeColors"
                type="radio"
                [checked]="settings.darkModeColor === dColor.value"
                class="hidden input-radio peer"
                />
                <label [for]="'noneColors_' + i" class="{{dColor.class}}">
                  <!-- <lucide-angular name="x" class="size-4"></lucide-angular> -->
                </label>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- *ngIf="settings.layout !== 'horizontal'" -->
        <ng-container>
          <div>
            <h6 class="my-4">Sidebar Asset Colors:</h6>
            <div class="flex flex-wrap items-center gap-3">
              <div class="input-radio-group" *ngFor="let clr of sideBarColor; let i = index">
                <input (change)="setSidebarColors(clr.value)" [id]="'lightSidebarColors_' + i"
                name="sidebarColors"
                type="radio"
                [checked]="settings.sidervarAssetColors === clr.value"
                class="hidden input-radio peer"
                />
                <label [for]="'lightSidebarColors_' + i" class="{{clr.class}}"></label>
              </div>
            </div>
          </div>
        </ng-container>

        <h6 class="my-4">Primary Asset Colors:</h6>
        <div class="flex flex-wrap items-center gap-3">
          <div class="input-radio-group" *ngFor="let pClr of primaryColor; let i = index">
            <input (change)="setPrimaryColors(pClr.value)" [id]="'defaultPrimaryColors_' + i"
            name="primaryColors"
            type="radio"
            [checked]="settings.primaryAssetColors === pClr.value"
            class="hidden input-radio peer"
            />
            <label [for]="'defaultPrimaryColors_' + i" class="{{pClr.class}}"></label>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end gap-2 modal-footer">
      <button type="button" class="btn btn-sub-gray" (click)="resetLayout()">
        <lucide-angular name="RotateCcw" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> Reset Layouts
      </button>
      <button type="button" class="btn btn-red">
        <lucide-angular name="ShoppingBag" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> Buy Now
      </button>
    </div>
  </div>
</div>