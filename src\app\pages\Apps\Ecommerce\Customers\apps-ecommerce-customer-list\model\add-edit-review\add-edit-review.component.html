<div class="modal">
  <div class="modal-wrap modal-center">
    <div class="p-0 modal-content">
      <div class="h-20 bg-gray-100 rounded-t-md dark:bg-dark-850"></div>
      <div class="modal-content" [formGroup]="reviewForm">
        <div class="-mt-16">
          <label for="logo">
            <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border border-gray-200 rounded-full cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-24">
              <img *ngIf="reviewForm.get('image')?.value" [src]="reviewForm.get('image')?.value" class="object-cover w-full h-full rounded-full">
              <div *ngIf="!reviewForm.get('image')?.value" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                <lucide-angular name="Upload"></lucide-angular>
              </div>
            </div>
          </label>
          <div class="hidden mt-4">
            <label class="block">
              <span class="sr-only">Choose profile photo</span>
              <input type="file" name="logo" id="logo" (change)="onFileChange($event)" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
            </label>
          </div>
          <span *ngIf="reviewForm.get('image')?.hasError('required')" class="text-sm text-red-500">image is
            required.</span>
        </div>

        <div class="mt-5">
          <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
              <label for="userNameInput" class="form-label">User Name</label>
              <input type="text" class="form-input" placeholder="User name" formControlName="customersName">
              <span *ngIf="reviewForm.get('customersName')?.hasError('required')" class="text-sm text-red-500">User name
                is required.</span>
            </div>
            <div class="col-span-12">
              <label for="emailInput" class="form-label">Email <span class="text-red-500">*</span></label>
              <input type="email" id="emailInput" class="form-input" placeholder="Enter your email" formControlName="email">
              <span *ngIf="reviewForm.get('email')?.hasError('required')" class="text-sm text-red-500">email is
                required.</span>
              <span *ngIf="reviewForm.get('email')?.hasError('pattern')" class="text-sm text-red-500">Please enter vaild
                email forrmat</span>
            </div>
            <div class="col-span-12">
              <label for="phoneNumberInput" class="form-label">Phone Number <span class="text-red-500">*</span></label>
              <input type="text" class="form-input" placeholder="************" formControlName="phoneNumber">
              <span *ngIf="reviewForm.get('phoneNumber')?.hasError('required')" class="text-red-500">This Field is Required</span>
              <span *ngIf="reviewForm.get('phoneNumber')?.hasError('pattern')" class="text-red-500">Invalid phone number format</span>
            </div>
            <div class="col-span-6">
              <label for="genderInput" class="form-label">Gender <span class="text-red-500">*</span></label>
              <div class="flex items-center gap-3">
                <div class="input-radio-group">
                  <input id="maleRadio" name="gender" class="input-radio input-radio-primary" type="radio" value="Male" x-model="customerForm.gender" formControlName="gender" />
                  <label for="maleRadio" name="gender" class="input-radio-label">Male</label>
                </div>
                <div class="input-radio-group">
                  <input id="femaleRadio" name="gender" class="input-radio input-radio-primary" type="radio" value="Female" x-model="customerForm.gender" formControlName="gender">
                  <label for="femaleRadio" name="gender" class="input-radio-label">Female</label>
                </div>
              </div>
              <span *ngIf="reviewForm.get('gender')?.hasError('required')" class="text-sm text-red-500">gender is
                required.</span>
            </div>
            <div class="col-span-6">
              <label for="subscriberSelect" class="form-label">Subscriber <span class="text-red-500">*</span></label>
              <select id="statusSelect" class="form-input" formControlName="subscriber">
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
              <span *ngIf="reviewForm.get('subscriber')?.hasError('required')" class="text-sm text-red-500">Subscriber
                is required.</span>
            </div>
            <div class="col-span-6">
              <label for="locationInput" class="form-label">Location</label>
              <input type="text" id="locationInput" class="form-input" formControlName="location" placeholder="Location">
              <span *ngIf="reviewForm.get('location')?.hasError('required')" class="text-sm text-red-500">Location is
                required.</span>
            </div>
            <div class="col-span-6">
              <label for="subscriberSelect" class="form-label">Status <span class="text-red-500">*</span></label>
              <select id="statusSelect" class="form-input" formControlName="status">
                <option value="Active">Active</option>
                <option value="Inactive">In Active</option>
              </select>
              <span *ngIf="reviewForm.get('subscriber')?.hasError('required')" class="text-sm text-red-500">Subscriber
                is required.</span>
            </div>
            <div class="col-span-12">
              <div class="flex justify-end gap-2">
                <button type="button" class="btn btn-active-red" (click)="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary" (click)="closeModal()"> {{config ? 'Edit' : 'Add' }}
                  Review</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="modal">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6>Add review</h6>
            <button class="link link-red float-end"  (click)=" closeModal()"><lucide-angular name="x" class="size-5"></lucide-angular></button>
        </div>
        <div class="modal-content">
            <form [formGroup]="reviewForm">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <div class="flex flex-col justify-center gap-5">
                            <h6 class="text-center">Your Rating?</h6>
                            <div class="relative flex justify-center gap-3">
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" [ngClass]="rating >= 1 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" (click)="setRating(1)" (mouseover)="hovering = 1" (mouseleave)="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" [ngClass]="rating == 1 || hovering == 1 ? '' : 'invisible'">😒</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" [ngClass]="rating >= 2 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" (click)="setRating(2)" (mouseover)="hovering = 2" (mouseleave)="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" [ngClass]="rating == 2 || hovering == 2 ? '' : 'invisible'">🤨</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" [ngClass]="rating >= 3 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" (click)="setRating(3)" (mouseover)="hovering = 3" (mouseleave)="hovering=0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" [ngClass]="rating == 3 || hovering == 3 ? '' : 'invisible'">😊</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" [ngClass]="rating >= 4 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" (click)="setRating(4)" (mouseover)="hovering = 4" (mouseleave)="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" [ngClass]="rating == 4 || hovering == 4 ? '' : 'invisible'">😚</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" [ngClass]="rating >= 5 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" (click)="setRating(5)" (mouseover)="hovering = 5" (mouseleave)="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" [ngClass]="rating == 5 || hovering == 5 ? '' : 'invisible'"> 🥰</p>
                                </div>
                            </div>
                            <div class="mt-5">
                                <label for="rating" class="form-label">Rating Input:</label>
                                <input id="rating" type="number" class="form-input" formControlName="star" (input)="changeRating()">
                                <span *ngIf="reviewForm.get('star')?.hasError('required')" class="text-sm text-red-500">Star is required.</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-6">
                        <label for="userNameInput" class="form-label">User Name</label>
                        <input type="text" id="userNameInput" class="form-input" formControlName="customersName" placeholder="User name">
                        <span *ngIf="reviewForm.get('customersName')?.hasError('required')" class="text-sm text-red-500">User name is required.</span>
                    </div>

                    <div class="col-span-6">
                        <label for="createDateInput" class="form-label">Create Date</label>
                        <input type="text" id="createDateInput" class="form-input" placeholder="DD-MM-YYYY" formControlName="date" data-provider="flatpickr" data-date-format="d M, Y" mwlFlatpickr>
                        <span *ngIf="reviewForm.get('date')?.hasError('required')" class="text-sm text-red-500">Date is required.</span>
                    </div>

                    <div class="col-span-12">
                        <label for="locationInput" class="form-label">Location</label>
                        <input type="text" id="locationInput" class="form-input" formControlName="location" placeholder="Location">
                        <span *ngIf="reviewForm.get('location')?.hasError('required')" class="text-sm text-red-500">Location is required.</span>
                    </div>

                    <div class="col-span-12">
                        <label for="titleInput" class="form-label">Title</label>
                        <input type="text" id="titleInput" class="form-input" formControlName="title" placeholder="Review title">
                        <span *ngIf="reviewForm.get('title')?.hasError('required')" class="text-sm text-red-500">Title is required.</span>
                    </div>

                    <div class="col-span-12">
                        <label for="writeReviewInput" class="form-label">Write your Content</label>
                        <textarea name="writeReviewInput" id="writeReviewInput" rows="3" class="h-auto form-input" formControlName="content" placeholder="Enter your description"></textarea>
                        <span *ngIf="reviewForm.get('content')?.hasError('required')" class="text-sm text-red-500">Content is required.</span>
                    </div>

                </div>
            </form>
            <div class="flex justify-end gap-2 mt-5">
                <button type="button" class="btn btn-active-red" (click)="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary" (click)="onSubmit()">Add Review</button>
            </div>
        </div>
    </div>
</div> -->