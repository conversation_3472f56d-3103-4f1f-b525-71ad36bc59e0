{"users": [{"id": 1, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-5.png"}, {"id": 2, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-6.png"}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "avatar": "assets/images/avatar/user-37.png"}, {"id": 4, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-36.png"}, {"id": 5, "name": "<PERSON><PERSON><PERSON>", "avatar": "assets/images/avatar/user-9.png"}, {"id": 6, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-29.png"}, {"id": 7, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-31.png"}, {"id": 8, "name": "<PERSON>", "avatar": "assets/images/avatar/user-12.png"}, {"id": 9, "name": "<PERSON><PERSON><PERSON>", "avatar": "assets/images/avatar/user-21.png"}, {"id": 10, "name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-14.png"}], "chats": [{"name": "<PERSON>", "avatar": "assets/images/avatar/user-13.png", "status": "online", "lastMessage": "Hello, How are you?", "timestamp": "Just Now", "unread": 2}, {"name": "<PERSON>", "avatar": "assets/images/avatar/user-11.png", "status": "online", "lastMessage": "Here are some of very cute illustration.", "timestamp": "30 min", "unread": 0}, {"name": "<PERSON>", "avatar": "assets/images/avatar/user-18.png", "status": "offline", "lastMessage": "Use tools like Trello, Asana, or Jira for task management and progress tracking.", "timestamp": "Just Now", "unread": 2}, {"name": "Tyron Derby", "avatar": "assets/images/avatar/user-20.png", "status": "offline", "lastMessage": "Regularly review and improve communication practices based on team feedback and project needs.", "timestamp": "Just Now", "unread": 0}, {"name": "<PERSON>", "avatarText": "SL", "status": "online", "lastMessage": "Schedule regular check-ins to address any roadblocks and keep everyone aligned.", "timestamp": "Yesterday", "unread": 0}, {"name": "<PERSON>", "avatarText": "JD", "status": "online", "lastMessage": "No further questions.", "timestamp": "Yesterday", "unread": 0}, {"name": "<PERSON>", "avatar": "assets/images/avatar/user-3.png", "status": "offline", "lastMessage": "Sure, I can help with that. Let's have a quick call after this meeting to debug the issue.", "timestamp": "Monday", "unread": 1}, {"name": "<PERSON>", "avatarText": "LB", "status": "offline", "lastMessage": "I'll share the meeting minutes and action items shortly.", "timestamp": "Monday", "unread": 0}, {"name": "<PERSON><PERSON>", "avatar": "assets/images/avatar/user-6.png", "status": "offline", "lastMessage": "Let's reconvene next week for our regular check-in. Have a productive week!", "timestamp": "Saturday", "unread": 0}], "messages": [{"sender": "<PERSON>", "time": "Today, 09:59 AM", "text": "We need a new website that allows users to create accounts, browse products, and make purchases. Can you provide a rough timeline and cost estimate?", "avatar": "assets/images/avatar/user-13.png", "type": "received"}, {"sender": "You", "time": "Today, 10:00 AM", "text": "Sure, we can help with that. To provide an accurate estimate, we'll need more details on the features you want. Let's schedule a call this week to discuss the specifics, such as the types of products, payment methods, and any design preferences.", "avatar": "assets/images/avatar/user-17.png", "type": "sent"}, {"sender": "You", "time": "Today, 10:15 AM", "text": "Got it. I’ll investigate and update you shortly. <a href=\"#\" class=\"text-red-500\">#bug</a>", "avatar": "assets/images/avatar/user-17.png", "type": "sent"}, {"sender": "<PERSON>", "time": "Today, 10:11 AM", "text": "Hi <a href=\"#\" class=\"text-primary-500\">@Shopia</a>, can you add the new search feature by Friday? Details are in the #features channel. Thanks! <a href=\"#\" class=\"text-primary-500\">#task</a>", "avatar": "assets/images/avatar/user-13.png", "type": "received"}, {"sender": "You", "time": "Today, 10:12 AM", "text": "Sure, starting on it today. Will update you on the progress. <a href=\"#\" class=\"text-primary-500\">#task154</a>", "avatar": "assets/images/avatar/user-17.png", "type": "sent"}, {"sender": "<PERSON>", "time": "Today, 02:39 PM", "text": "Hi <PERSON><PERSON>, there’s a problem with the mobile view on the homepage. Images aren’t scaling right. Can someone check? <a href=\"#\" class=\"text-red-500\">#bug</a>", "avatar": "assets/images/avatar/user-13.png", "type": "received", "images": ["assets/images/gallery/img-01.jpg", "assets/images/gallery/img-05.jpg"], "extraImagesCount": "2+"}]}