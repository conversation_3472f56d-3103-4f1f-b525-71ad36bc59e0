// const plugin = require('tailwindcss/plugin')

// module.exports = plugin(function ({ addComponents }) {
//     addComponents({
//         'body': {
//             '@apply bg-[url("../images/bg.jpg")] bg-cover bg-fixed before:fixed before:inset-0 before:bg-dark-950/80 text-dark-50':{},
//         },
//         '.card': {
//             '@apply dark:bg-dark-950/50 dark:border-dark-900 dark:shadow-none before:absolute before:border-l-2 before:border-t-2 before:border-primary-500/30 before:-mt-2 before:-ml-2 relative before:size-16':{},
//         },
//         '.card-header': { 
//             '@apply dark:border-dark-900':{},
//         },
//         '.main-sidebar': { 
//             '@apply dark:bg-dark-950/30 dark:border-dark-900 dark:shadow-none':{},
//         }
//     })
// })