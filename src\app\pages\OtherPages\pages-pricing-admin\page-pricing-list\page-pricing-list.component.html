<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-x-space">
  <div class="relative overflow-hidden card">
    <div class="absolute inset-0 opacity-90">
      <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600" class="w-full">
        <title>1274-ai</title>
        <defs>
          <clipPath clipPathUnits="userSpaceOnUse" id="cp1">
            <path d="m0 0h600v600h-600z" />
          </clipPath>
        </defs>
        <g id="_Artboards_">
        </g>
        <g id="Layer 1">
          <g id="&lt;Clip Group&gt;" clip-path="url(#cp1)">
            <g id="&lt;Group&gt;">
              <g id="all 10">
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m26.5 31.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m25.1 92.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m23.7 152.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m22.3 213.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m20.9 274.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m19.5 334.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m18.1 395.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m16.7 456.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m15.3 516.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m13.9 577.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.9 6.9-18.9-18.9-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m86.8 31.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m85.5 92.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m84.2 152.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m83 213.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m81.7 273.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m80.4 334.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m79.1 395.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m77.8 455.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m76.6 516.3l7.1-19.7 19.7 7.1-7.1 19.7zm2.6-1.2l15.8 5.7 5.7-15.7-15.7-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m75.3 576.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m147.1 31.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m145.9 91.9l2.2-6 5.9 2.2-2.1 5.9zm0.8-0.3l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m144.8 152.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m143.6 213l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m142.5 273.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m141.3 334l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m140.2 394.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m139 455.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1l12.9 4.7 4.7-12.9-12.9-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m137.8 515.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m136.7 576.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m207.4 31.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m206.4 91.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m205.3 152.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m204.3 212.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m203.3 273.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m202.2 333.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m201.2 394.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.8-0.8l10.3 3.7 3.7-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m200.2 454.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m199.1 515.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m198.1 575.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m267.7 31.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m266.8 91.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m265.9 151.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m265 212.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m264.1 272.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m263.1 333.2l3.7-10.1 10.1 3.7-3.7 10.1zm1.4-0.7l8 2.9 2.9-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m262.2 393.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m261.3 454l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m260.4 514.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m259.5 574.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m328 30.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m327.2 91.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m326.4 151.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m325.6 212l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m324.9 272.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.4l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m324.1 332.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m323.3 393.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m322.5 453.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m321.7 513.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m320.9 574.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m388.3 30.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m387.7 91.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m387 151.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m386.3 211.7l2-5.4 5.4 2-2 5.4zm0.8-0.4l4.3 1.6 1.6-4.3-4.4-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m385.6 272l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m385 332.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m384.3 392.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m383.6 453l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m383 513.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m382.3 573.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m448.6 30.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m448.1 90.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m447.5 151.2l1.3-3.7 3.7 1.3-1.3 3.7zm0.4-0.3l2.9 1.1 1.1-2.9-2.9-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m447 211.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m446.4 271.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m445.9 331.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m445.3 392.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m444.8 452.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m444.2 512.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m443.7 572.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m508.9 30.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m508.5 90.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.1l1.7 0.6 0.7-1.8-1.8-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m508.1 150.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m507.7 211.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m507.2 271.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m506.8 331.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m506.4 391.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m505.9 451.9l2.2-5.9 5.9 2.1-2.1 6zm0.8-0.4l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m505.5 512.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m505.1 572.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m569.2 30.4l0.4-1.2 1.2 0.4-0.4 1.2zm0.1-0.1l0.9 0.3 0.3-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m568.9 90.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m568.6 150.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m568.3 210.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m568 270.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m567.7 331.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m567.4 391.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m567.1 451.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m566.8 511.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m566.5 571.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-2.9 1.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-4.4 62l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-5.8 122.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-7.3 183.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-8.8 244.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-10.2 304.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-11.7 365.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-13.2 426.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-14.6 486.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-16.1 547.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m-17.5 608.2l9.3-25.7 25.7 9.3-9.3 25.7zm3.5-1.6l20.6 7.5 7.5-20.6-20.6-7.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m57.3 1.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m56 61.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m54.6 122.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m53.3 183.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m52 243.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m50.6 304.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m49.3 365l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m47.9 425.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m46.6 486.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m45.3 546.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.3l17.3 6.3 6.3-17.3-17.3-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m43.9 607.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m117.6 1.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m116.3 61.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m115.1 122.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m113.9 182.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m112.7 243.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m111.5 304l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m110.3 364.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m109 425.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m107.8 485.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.3 5.2 5.2-14.2-14.3-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m106.6 546.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m105.4 606.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m177.8 1l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m176.7 61.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m175.6 122l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m174.5 182.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m173.4 243.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m172.3 303.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m171.2 364.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m170.1 424.6l5.3-14.5 14.5 5.3-5.3 14.5zm2-1l11.5 4.3 4.3-11.6-11.6-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m169 485.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m167.9 545.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m166.8 606.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m238.1 0.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m237.1 61.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m236.1 121.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m235.1 182.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m234.2 242.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m233.2 303.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m232.2 363.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.7l9.1 3.3 3.4-9.1-9.2-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m231.2 424.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m230.3 484.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m229.3 545l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m228.3 605.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m298.3 0.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m297.4 61.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m296.6 121.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m295.7 182l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m294.9 242.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m294 302.8l3.2-8.8 8.8 3.2-3.2 8.8zm1.2-0.6l7 2.6 2.5-7-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m293.2 363.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m292.3 423.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m291.5 484l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m290.6 544.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m289.8 604.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m358.5 0.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m357.8 61l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m357.1 121.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m356.3 181.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m355.6 242l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m354.9 302.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m354.2 362.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m353.4 423.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m352.7 483.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m352 543.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m351.2 604.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m418.8 0.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m418.2 60.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m417.6 121.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m417 181.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.2l3.6 1.3 1.3-3.6-3.6-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m416.3 241.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m415.7 302l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m415.1 362.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m414.5 422.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m413.9 482.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m413.3 543.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m412.7 603.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m479 0.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m478.5 60.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m478.1 120.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m477.6 181.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m477.1 241.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m476.6 301.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m476.1 361.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m475.6 422l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m475.1 482.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m474.6 542.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m474.2 602.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m539.3 0.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 100 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m538.9 60.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.5 0.5-1.3-1.3-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 101 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m538.5 120.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 102 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m538.2 180.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 103 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m537.8 241l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 104 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m537.4 301.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 105 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m537.1 361.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 106 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m536.7 421.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 107 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m536.3 481.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 108 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m536 541.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 109 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m535.6 602l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 110 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m599.5 0.2l0.3-0.7 0.7 0.3-0.3 0.7zm0.1-0.1l0.6 0.2 0.2-0.5-0.6-0.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 111 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m599.3 60.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 112 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m599 120.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 113 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m598.8 180.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 114 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m598.5 240.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.7 0.6-1.8-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 115 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m598.3 300.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 116 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m598.1 360.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 117 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m597.8 421l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 118 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m597.6 481.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 119 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m597.3 541.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 120 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-green-500/20"
                      d="m597.1 601.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div class="relative card-body">
      <h6 class="uppercase text-14">Active Plan</h6>
      <div class="flex items-center justify-center h-32">
        <div>
          <lucide-angular name="TrendingUp" class="mx-auto mb-2 text-green-500 size-7"></lucide-angular>
          <h3>+35.9%</h3>
        </div>
      </div>
      <h6>
        <lucide-angular name="UserRound" class="inline-block ltr:mr-1 rtl:ml-1 size-4 mb-0.5"></lucide-angular> 9725
      </h6>
    </div>
  </div>
  <div class="relative overflow-hidden card">
    <div class="absolute inset-0 opacity-90">
      <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600" class="w-full">
        <title>1274-ai</title>
        <defs>
          <clipPath clipPathUnits="userSpaceOnUse" id="cp1">
            <path d="m0 0h600v600h-600z" />
          </clipPath>
        </defs>
        <g id="_Artboards_">
        </g>
        <g id="Layer 1">
          <g id="&lt;Clip Group&gt;" clip-path="url(#cp1)">
            <g id="&lt;Group&gt;">
              <g id="all 10">
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m26.5 31.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m25.1 92.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m23.7 152.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m22.3 213.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m20.9 274.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m19.5 334.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m18.1 395.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m16.7 456.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m15.3 516.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m13.9 577.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.9 6.9-18.9-18.9-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m86.8 31.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m85.5 92.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m84.2 152.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m83 213.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m81.7 273.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m80.4 334.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m79.1 395.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m77.8 455.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m76.6 516.3l7.1-19.7 19.7 7.1-7.1 19.7zm2.6-1.2l15.8 5.7 5.7-15.7-15.7-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m75.3 576.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m147.1 31.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m145.9 91.9l2.2-6 5.9 2.2-2.1 5.9zm0.8-0.3l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m144.8 152.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m143.6 213l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m142.5 273.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m141.3 334l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m140.2 394.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m139 455.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1l12.9 4.7 4.7-12.9-12.9-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m137.8 515.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m136.7 576.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m207.4 31.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m206.4 91.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m205.3 152.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m204.3 212.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m203.3 273.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m202.2 333.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m201.2 394.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.8-0.8l10.3 3.7 3.7-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m200.2 454.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m199.1 515.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m198.1 575.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m267.7 31.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m266.8 91.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m265.9 151.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m265 212.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m264.1 272.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m263.1 333.2l3.7-10.1 10.1 3.7-3.7 10.1zm1.4-0.7l8 2.9 2.9-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m262.2 393.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m261.3 454l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m260.4 514.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m259.5 574.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m328 30.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m327.2 91.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m326.4 151.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m325.6 212l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m324.9 272.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.4l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m324.1 332.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m323.3 393.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m322.5 453.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m321.7 513.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m320.9 574.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m388.3 30.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m387.7 91.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m387 151.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m386.3 211.7l2-5.4 5.4 2-2 5.4zm0.8-0.4l4.3 1.6 1.6-4.3-4.4-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m385.6 272l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m385 332.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m384.3 392.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m383.6 453l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m383 513.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m382.3 573.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m448.6 30.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m448.1 90.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m447.5 151.2l1.3-3.7 3.7 1.3-1.3 3.7zm0.4-0.3l2.9 1.1 1.1-2.9-2.9-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m447 211.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m446.4 271.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m445.9 331.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m445.3 392.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m444.8 452.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m444.2 512.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m443.7 572.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m508.9 30.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m508.5 90.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.1l1.7 0.6 0.7-1.8-1.8-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m508.1 150.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m507.7 211.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m507.2 271.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m506.8 331.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m506.4 391.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m505.9 451.9l2.2-5.9 5.9 2.1-2.1 6zm0.8-0.4l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m505.5 512.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m505.1 572.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m569.2 30.4l0.4-1.2 1.2 0.4-0.4 1.2zm0.1-0.1l0.9 0.3 0.3-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m568.9 90.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m568.6 150.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m568.3 210.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m568 270.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m567.7 331.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m567.4 391.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m567.1 451.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m566.8 511.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m566.5 571.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-2.9 1.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-4.4 62l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-5.8 122.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-7.3 183.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-8.8 244.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-10.2 304.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-11.7 365.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-13.2 426.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-14.6 486.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-16.1 547.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m-17.5 608.2l9.3-25.7 25.7 9.3-9.3 25.7zm3.5-1.6l20.6 7.5 7.5-20.6-20.6-7.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m57.3 1.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m56 61.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m54.6 122.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m53.3 183.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m52 243.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m50.6 304.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m49.3 365l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m47.9 425.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m46.6 486.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m45.3 546.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.3l17.3 6.3 6.3-17.3-17.3-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m43.9 607.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m117.6 1.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m116.3 61.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m115.1 122.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m113.9 182.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m112.7 243.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m111.5 304l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m110.3 364.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m109 425.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m107.8 485.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.3 5.2 5.2-14.2-14.3-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m106.6 546.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m105.4 606.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m177.8 1l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m176.7 61.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m175.6 122l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m174.5 182.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m173.4 243.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m172.3 303.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m171.2 364.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m170.1 424.6l5.3-14.5 14.5 5.3-5.3 14.5zm2-1l11.5 4.3 4.3-11.6-11.6-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m169 485.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m167.9 545.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m166.8 606.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m238.1 0.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m237.1 61.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m236.1 121.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m235.1 182.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m234.2 242.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m233.2 303.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m232.2 363.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.7l9.1 3.3 3.4-9.1-9.2-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m231.2 424.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m230.3 484.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m229.3 545l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m228.3 605.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m298.3 0.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m297.4 61.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m296.6 121.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m295.7 182l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m294.9 242.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m294 302.8l3.2-8.8 8.8 3.2-3.2 8.8zm1.2-0.6l7 2.6 2.5-7-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m293.2 363.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m292.3 423.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m291.5 484l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m290.6 544.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m289.8 604.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m358.5 0.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m357.8 61l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m357.1 121.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m356.3 181.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m355.6 242l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m354.9 302.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m354.2 362.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m353.4 423.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m352.7 483.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m352 543.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m351.2 604.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m418.8 0.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m418.2 60.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m417.6 121.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m417 181.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.2l3.6 1.3 1.3-3.6-3.6-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m416.3 241.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m415.7 302l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m415.1 362.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m414.5 422.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m413.9 482.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m413.3 543.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m412.7 603.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m479 0.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m478.5 60.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m478.1 120.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m477.6 181.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m477.1 241.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m476.6 301.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m476.1 361.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m475.6 422l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m475.1 482.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m474.6 542.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m474.2 602.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m539.3 0.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 100 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m538.9 60.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.5 0.5-1.3-1.3-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 101 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m538.5 120.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 102 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m538.2 180.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 103 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m537.8 241l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 104 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m537.4 301.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 105 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m537.1 361.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 106 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m536.7 421.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 107 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m536.3 481.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 108 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m536 541.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 109 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m535.6 602l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 110 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m599.5 0.2l0.3-0.7 0.7 0.3-0.3 0.7zm0.1-0.1l0.6 0.2 0.2-0.5-0.6-0.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 111 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m599.3 60.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 112 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m599 120.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 113 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m598.8 180.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 114 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m598.5 240.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.7 0.6-1.8-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 115 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m598.3 300.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 116 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m598.1 360.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 117 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m597.8 421l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 118 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m597.6 481.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 119 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m597.3 541.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 120 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-red-500/20"
                      d="m597.1 601.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div class="relative card-body">
      <h6 class="uppercase text-14">UnActive Plan</h6>
      <div class="flex items-center justify-center h-32">
        <div>
          <lucide-angular name="TrendingDown" class="mx-auto mb-2 text-red-500 size-7"></lucide-angular>
          <h3>-6.4%</h3>
        </div>
      </div>
      <h6>
        <lucide-angular name="UserRound" class="inline-block ltr:mr-1 rtl:ml-1 size-4 mb-0.5"></lucide-angular> 1020
      </h6>
    </div>
  </div>
  <div class="relative overflow-hidden card">
    <div class="absolute inset-0 opacity-90">
      <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600" class="w-full">
        <title>1274-ai</title>
        <defs>
          <clipPath clipPathUnits="userSpaceOnUse" id="cp1">
            <path d="m0 0h600v600h-600z" />
          </clipPath>
        </defs>
        <g id="_Artboards_">
        </g>
        <g id="Layer 1">
          <g id="&lt;Clip Group&gt;" clip-path="url(#cp1)">
            <g id="&lt;Group&gt;">
              <g id="all 10">
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m26.5 31.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m25.1 92.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m23.7 152.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m22.3 213.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m20.9 274.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m19.5 334.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m18.1 395.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m16.7 456.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m15.3 516.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m13.9 577.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.9 6.9-18.9-18.9-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m86.8 31.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m85.5 92.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m84.2 152.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m83 213.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m81.7 273.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m80.4 334.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m79.1 395.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m77.8 455.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m76.6 516.3l7.1-19.7 19.7 7.1-7.1 19.7zm2.6-1.2l15.8 5.7 5.7-15.7-15.7-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m75.3 576.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m147.1 31.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m145.9 91.9l2.2-6 5.9 2.2-2.1 5.9zm0.8-0.3l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m144.8 152.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m143.6 213l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m142.5 273.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m141.3 334l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m140.2 394.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m139 455.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1l12.9 4.7 4.7-12.9-12.9-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m137.8 515.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m136.7 576.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m207.4 31.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m206.4 91.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m205.3 152.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m204.3 212.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m203.3 273.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m202.2 333.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m201.2 394.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.8-0.8l10.3 3.7 3.7-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m200.2 454.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m199.1 515.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m198.1 575.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m267.7 31.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m266.8 91.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m265.9 151.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m265 212.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m264.1 272.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m263.1 333.2l3.7-10.1 10.1 3.7-3.7 10.1zm1.4-0.7l8 2.9 2.9-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m262.2 393.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m261.3 454l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m260.4 514.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m259.5 574.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m328 30.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m327.2 91.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m326.4 151.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m325.6 212l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m324.9 272.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.4l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m324.1 332.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m323.3 393.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m322.5 453.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m321.7 513.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m320.9 574.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m388.3 30.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m387.7 91.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m387 151.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m386.3 211.7l2-5.4 5.4 2-2 5.4zm0.8-0.4l4.3 1.6 1.6-4.3-4.4-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m385.6 272l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m385 332.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m384.3 392.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m383.6 453l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m383 513.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m382.3 573.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m448.6 30.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m448.1 90.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m447.5 151.2l1.3-3.7 3.7 1.3-1.3 3.7zm0.4-0.3l2.9 1.1 1.1-2.9-2.9-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m447 211.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m446.4 271.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m445.9 331.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m445.3 392.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m444.8 452.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m444.2 512.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m443.7 572.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m508.9 30.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m508.5 90.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.1l1.7 0.6 0.7-1.8-1.8-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m508.1 150.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m507.7 211.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m507.2 271.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m506.8 331.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m506.4 391.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m505.9 451.9l2.2-5.9 5.9 2.1-2.1 6zm0.8-0.4l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m505.5 512.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m505.1 572.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m569.2 30.4l0.4-1.2 1.2 0.4-0.4 1.2zm0.1-0.1l0.9 0.3 0.3-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m568.9 90.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m568.6 150.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m568.3 210.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m568 270.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m567.7 331.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m567.4 391.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m567.1 451.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m566.8 511.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m566.5 571.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-2.9 1.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-4.4 62l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-5.8 122.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-7.3 183.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-8.8 244.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-10.2 304.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-11.7 365.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-13.2 426.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-14.6 486.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-16.1 547.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m-17.5 608.2l9.3-25.7 25.7 9.3-9.3 25.7zm3.5-1.6l20.6 7.5 7.5-20.6-20.6-7.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m57.3 1.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m56 61.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m54.6 122.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m53.3 183.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m52 243.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m50.6 304.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m49.3 365l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m47.9 425.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m46.6 486.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m45.3 546.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.3l17.3 6.3 6.3-17.3-17.3-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m43.9 607.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m117.6 1.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m116.3 61.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m115.1 122.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m113.9 182.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m112.7 243.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m111.5 304l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m110.3 364.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m109 425.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m107.8 485.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.3 5.2 5.2-14.2-14.3-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m106.6 546.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m105.4 606.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m177.8 1l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m176.7 61.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m175.6 122l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m174.5 182.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m173.4 243.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m172.3 303.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m171.2 364.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m170.1 424.6l5.3-14.5 14.5 5.3-5.3 14.5zm2-1l11.5 4.3 4.3-11.6-11.6-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m169 485.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m167.9 545.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m166.8 606.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m238.1 0.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m237.1 61.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m236.1 121.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m235.1 182.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m234.2 242.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m233.2 303.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m232.2 363.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.7l9.1 3.3 3.4-9.1-9.2-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m231.2 424.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m230.3 484.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m229.3 545l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m228.3 605.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m298.3 0.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m297.4 61.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m296.6 121.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m295.7 182l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m294.9 242.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m294 302.8l3.2-8.8 8.8 3.2-3.2 8.8zm1.2-0.6l7 2.6 2.5-7-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m293.2 363.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m292.3 423.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m291.5 484l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m290.6 544.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m289.8 604.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m358.5 0.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m357.8 61l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m357.1 121.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m356.3 181.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m355.6 242l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m354.9 302.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m354.2 362.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m353.4 423.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m352.7 483.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m352 543.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m351.2 604.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m418.8 0.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m418.2 60.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m417.6 121.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m417 181.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.2l3.6 1.3 1.3-3.6-3.6-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m416.3 241.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m415.7 302l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m415.1 362.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m414.5 422.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m413.9 482.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m413.3 543.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m412.7 603.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m479 0.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m478.5 60.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m478.1 120.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m477.6 181.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m477.1 241.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m476.6 301.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m476.1 361.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m475.6 422l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m475.1 482.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m474.6 542.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m474.2 602.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m539.3 0.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 100 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m538.9 60.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.5 0.5-1.3-1.3-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 101 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m538.5 120.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 102 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m538.2 180.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 103 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m537.8 241l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 104 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m537.4 301.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 105 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m537.1 361.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 106 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m536.7 421.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 107 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m536.3 481.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 108 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m536 541.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 109 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m535.6 602l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 110 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m599.5 0.2l0.3-0.7 0.7 0.3-0.3 0.7zm0.1-0.1l0.6 0.2 0.2-0.5-0.6-0.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 111 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m599.3 60.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 112 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m599 120.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 113 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m598.8 180.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 114 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m598.5 240.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.7 0.6-1.8-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 115 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m598.3 300.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 116 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m598.1 360.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 117 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m597.8 421l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 118 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m597.6 481.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 119 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m597.3 541.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 120 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-primary-500/20"
                      d="m597.1 601.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div class="relative card-body">
      <h6 class="uppercase text-14">Regular Plan</h6>
      <div class="flex items-center justify-center h-32">
        <div>
          <lucide-angular name="TrendingUp" class="mx-auto mb-2 text-green-500 size-7"></lucide-angular>
          <h3>+6.3%</h3>
        </div>
      </div>
      <h6>
        <lucide-angular name="UserRound" class="inline-block ltr:mr-1 rtl:ml-1 size-4 mb-0.5"></lucide-angular> 1450
      </h6>
    </div>
  </div>
  <div class="relative overflow-hidden card">
    <div class="absolute inset-0 opacity-90">
      <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600" class="w-full">
        <title>1274-ai</title>
        <defs>
          <clipPath clipPathUnits="userSpaceOnUse" id="cp1">
            <path d="m0 0h600v600h-600z" />
          </clipPath>
        </defs>
        <g id="_Artboards_">
        </g>
        <g id="Layer 1">
          <g id="&lt;Clip Group&gt;" clip-path="url(#cp1)">
            <g id="&lt;Group&gt;">
              <g id="all 10">
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m26.5 31.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m25.1 92.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m23.7 152.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m22.3 213.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m20.9 274.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m19.5 334.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m18.1 395.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m16.7 456.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m15.3 516.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m13.9 577.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.9 6.9-18.9-18.9-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m86.8 31.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m85.5 92.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m84.2 152.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m83 213.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m81.7 273.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m80.4 334.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m79.1 395.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m77.8 455.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m76.6 516.3l7.1-19.7 19.7 7.1-7.1 19.7zm2.6-1.2l15.8 5.7 5.7-15.7-15.7-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m75.3 576.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m147.1 31.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m145.9 91.9l2.2-6 5.9 2.2-2.1 5.9zm0.8-0.3l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m144.8 152.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m143.6 213l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m142.5 273.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m141.3 334l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m140.2 394.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m139 455.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1l12.9 4.7 4.7-12.9-12.9-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m137.8 515.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m136.7 576.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m207.4 31.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m206.4 91.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m205.3 152.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m204.3 212.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m203.3 273.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m202.2 333.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m201.2 394.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.8-0.8l10.3 3.7 3.7-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m200.2 454.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m199.1 515.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m198.1 575.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m267.7 31.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m266.8 91.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m265.9 151.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m265 212.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m264.1 272.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m263.1 333.2l3.7-10.1 10.1 3.7-3.7 10.1zm1.4-0.7l8 2.9 2.9-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m262.2 393.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m261.3 454l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m260.4 514.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m259.5 574.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m328 30.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m327.2 91.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m326.4 151.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m325.6 212l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m324.9 272.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.4l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m324.1 332.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m323.3 393.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m322.5 453.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m321.7 513.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m320.9 574.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m388.3 30.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m387.7 91.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m387 151.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m386.3 211.7l2-5.4 5.4 2-2 5.4zm0.8-0.4l4.3 1.6 1.6-4.3-4.4-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m385.6 272l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m385 332.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m384.3 392.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m383.6 453l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m383 513.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m382.3 573.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m448.6 30.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m448.1 90.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m447.5 151.2l1.3-3.7 3.7 1.3-1.3 3.7zm0.4-0.3l2.9 1.1 1.1-2.9-2.9-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m447 211.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m446.4 271.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m445.9 331.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m445.3 392.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m444.8 452.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m444.2 512.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m443.7 572.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m508.9 30.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m508.5 90.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.1l1.7 0.6 0.7-1.8-1.8-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m508.1 150.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m507.7 211.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m507.2 271.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m506.8 331.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m506.4 391.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m505.9 451.9l2.2-5.9 5.9 2.1-2.1 6zm0.8-0.4l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m505.5 512.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m505.1 572.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m569.2 30.4l0.4-1.2 1.2 0.4-0.4 1.2zm0.1-0.1l0.9 0.3 0.3-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m568.9 90.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m568.6 150.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m568.3 210.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m568 270.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m567.7 331.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m567.4 391.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m567.1 451.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m566.8 511.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m566.5 571.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-2.9 1.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-4.4 62l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-5.8 122.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-7.3 183.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-8.8 244.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-10.2 304.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-11.7 365.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-13.2 426.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-14.6 486.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-16.1 547.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m-17.5 608.2l9.3-25.7 25.7 9.3-9.3 25.7zm3.5-1.6l20.6 7.5 7.5-20.6-20.6-7.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m57.3 1.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m56 61.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m54.6 122.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m53.3 183.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m52 243.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m50.6 304.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m49.3 365l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m47.9 425.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m46.6 486.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m45.3 546.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.3l17.3 6.3 6.3-17.3-17.3-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m43.9 607.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m117.6 1.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m116.3 61.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m115.1 122.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m113.9 182.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m112.7 243.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m111.5 304l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m110.3 364.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m109 425.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m107.8 485.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.3 5.2 5.2-14.2-14.3-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m106.6 546.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m105.4 606.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m177.8 1l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m176.7 61.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m175.6 122l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m174.5 182.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m173.4 243.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m172.3 303.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m171.2 364.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m170.1 424.6l5.3-14.5 14.5 5.3-5.3 14.5zm2-1l11.5 4.3 4.3-11.6-11.6-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m169 485.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m167.9 545.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m166.8 606.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m238.1 0.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m237.1 61.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m236.1 121.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m235.1 182.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m234.2 242.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m233.2 303.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m232.2 363.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.7l9.1 3.3 3.4-9.1-9.2-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m231.2 424.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m230.3 484.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m229.3 545l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m228.3 605.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m298.3 0.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m297.4 61.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m296.6 121.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m295.7 182l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m294.9 242.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m294 302.8l3.2-8.8 8.8 3.2-3.2 8.8zm1.2-0.6l7 2.6 2.5-7-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m293.2 363.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m292.3 423.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m291.5 484l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m290.6 544.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m289.8 604.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m358.5 0.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m357.8 61l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m357.1 121.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m356.3 181.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m355.6 242l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m354.9 302.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m354.2 362.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m353.4 423.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m352.7 483.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m352 543.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m351.2 604.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m418.8 0.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m418.2 60.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m417.6 121.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m417 181.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.2l3.6 1.3 1.3-3.6-3.6-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m416.3 241.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m415.7 302l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m415.1 362.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m414.5 422.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m413.9 482.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m413.3 543.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m412.7 603.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m479 0.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m478.5 60.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m478.1 120.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m477.6 181.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m477.1 241.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m476.6 301.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m476.1 361.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m475.6 422l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m475.1 482.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m474.6 542.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m474.2 602.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m539.3 0.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 100 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m538.9 60.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.5 0.5-1.3-1.3-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 101 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m538.5 120.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 102 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m538.2 180.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 103 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m537.8 241l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 104 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m537.4 301.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 105 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m537.1 361.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 106 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m536.7 421.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 107 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m536.3 481.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 108 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m536 541.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 109 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m535.6 602l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 110 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m599.5 0.2l0.3-0.7 0.7 0.3-0.3 0.7zm0.1-0.1l0.6 0.2 0.2-0.5-0.6-0.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 111 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m599.3 60.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 112 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m599 120.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 113 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m598.8 180.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 114 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m598.5 240.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.7 0.6-1.8-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 115 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m598.3 300.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 116 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m598.1 360.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 117 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m597.8 421l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 118 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m597.6 481.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 119 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m597.3 541.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 120 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-purple-500/20"
                      d="m597.1 601.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div class="relative card-body">
      <h6 class="uppercase text-14">Professional Plan</h6>
      <div class="flex items-center justify-center h-32">
        <div>
          <lucide-angular name="TrendingUp" class="mx-auto mb-2 text-green-500 size-7"></lucide-angular>
          <h3>+10.7%</h3>
        </div>
      </div>
      <h6>
        <lucide-angular name="UserRound" class="inline-block ltr:mr-1 rtl:ml-1 size-4 mb-0.5"></lucide-angular> 4784
      </h6>
    </div>
  </div>
  <div class="relative overflow-hidden card">
    <div class="absolute inset-0 opacity-90">
      <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600" class="w-full">
        <title>1274-ai</title>
        <defs>
          <clipPath clipPathUnits="userSpaceOnUse" id="cp1">
            <path d="m0 0h600v600h-600z" />
          </clipPath>
        </defs>
        <g id="_Artboards_">
        </g>
        <g id="Layer 1">
          <g id="&lt;Clip Group&gt;" clip-path="url(#cp1)">
            <g id="&lt;Group&gt;">
              <g id="all 10">
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m26.5 31.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m25.1 92.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m23.7 152.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m22.3 213.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m20.9 274.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m19.5 334.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m18.1 395.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m16.7 456.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m15.3 516.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m13.9 577.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.9 6.9-18.9-18.9-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m86.8 31.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m85.5 92.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m84.2 152.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m83 213.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m81.7 273.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m80.4 334.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m79.1 395.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m77.8 455.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m76.6 516.3l7.1-19.7 19.7 7.1-7.1 19.7zm2.6-1.2l15.8 5.7 5.7-15.7-15.7-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m75.3 576.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.4l17.3 6.3 6.2-17.2-17.2-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m147.1 31.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m145.9 91.9l2.2-6 5.9 2.2-2.1 5.9zm0.8-0.3l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m144.8 152.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m143.6 213l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m142.5 273.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m141.3 334l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m140.2 394.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m139 455.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1l12.9 4.7 4.7-12.9-12.9-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m137.8 515.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.2 5.2 5.2-14.2-14.2-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m136.7 576.2l7.1-19.5 19.5 7.1-7.1 19.5zm2.7-1.2l15.6 5.6 5.7-15.6-15.7-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m207.4 31.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m206.4 91.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m205.3 152.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m204.3 212.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m203.3 273.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m202.2 333.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m201.2 394.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.8-0.8l10.3 3.7 3.7-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m200.2 454.6l5.2-14.4 14.4 5.2-5.2 14.4zm2-0.9l11.5 4.2 4.2-11.5-11.5-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m199.1 515.1l5.8-16 16 5.8-5.8 16zm2.1-1l12.8 4.6 4.6-12.8-12.7-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m198.1 575.6l6.3-17.5 17.5 6.3-6.3 17.5zm2.3-1.1l14 5.1 5.1-14-14-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m267.7 31.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m266.8 91.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m265.9 151.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m265 212.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m264.1 272.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m263.1 333.2l3.7-10.1 10.1 3.7-3.7 10.1zm1.4-0.7l8 2.9 2.9-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m262.2 393.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.8l9.1 3.4 3.3-9.2-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m261.3 454l4.7-12.7 12.7 4.7-4.7 12.7zm1.8-0.8l10.2 3.7 3.7-10.2-10.2-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m260.4 514.5l5.1-14.1 14.1 5.1-5.1 14.1zm1.9-0.9l11.2 4.1 4.1-11.3-11.2-4.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m259.5 574.9l5.6-15.4 15.4 5.6-5.6 15.4zm2.1-1l12.3 4.5 4.5-12.3-12.3-4.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m328 30.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m327.2 91.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m326.4 151.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m325.6 212l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m324.9 272.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.4l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m324.1 332.8l3.1-8.7 8.7 3.1-3.1 8.7zm1.2-0.5l6.9 2.5 2.6-6.9-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m323.3 393.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.9 2.9 2.9-7.9-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m322.5 453.5l4-11 11 4-4 11zm1.5-0.7l8.8 3.2 3.2-8.8-8.8-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m321.7 513.9l4.4-12.2 12.2 4.4-4.4 12.2zm1.6-0.8l9.8 3.6 3.5-9.8-9.7-3.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m320.9 574.2l4.9-13.3 13.3 4.9-4.9 13.3zm1.9-0.8l10.6 3.8 3.9-10.6-10.6-3.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m388.3 30.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m387.7 91.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m387 151.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m386.3 211.7l2-5.4 5.4 2-2 5.4zm0.8-0.4l4.3 1.6 1.6-4.3-4.4-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m385.6 272l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.5l5.1 1.9 1.9-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m385 332.3l2.7-7.3 7.3 2.7-2.7 7.3zm1-0.4l5.9 2.1 2.2-5.9-5.9-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m384.3 392.7l3-8.4 8.4 3-3 8.4zm1.1-0.5l6.7 2.4 2.4-6.7-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m383.6 453l3.4-9.4 9.4 3.4-3.4 9.4zm1.2-0.6l7.5 2.7 2.7-7.5-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m383 513.3l3.7-10.3 10.3 3.7-3.7 10.3zm1.4-0.6l8.2 3 3-8.3-8.2-3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m382.3 573.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9.1 3.3 3.3-9.1-9.1-3.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m448.6 30.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m448.1 90.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m447.5 151.2l1.3-3.7 3.7 1.3-1.3 3.7zm0.4-0.3l2.9 1.1 1.1-2.9-2.9-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m447 211.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.3l3.5 1.3 1.3-3.5-3.5-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m446.4 271.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.4l4.2 1.6 1.5-4.2-4.2-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m445.9 331.9l2.2-6 6 2.2-2.2 6zm0.8-0.4l4.9 1.8 1.7-4.8-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m445.3 392.2l2.5-6.9 6.9 2.5-2.5 6.9zm0.9-0.5l5.5 2 2-5.5-5.5-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m444.8 452.4l2.8-7.6 7.6 2.8-2.8 7.6zm1.1-0.5l6.1 2.2 2.2-6.1-6.1-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m444.2 512.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.1-0.6l6.8 2.5 2.4-6.8-6.7-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m443.7 572.9l3.4-9.2 9.2 3.4-3.4 9.2zm1.3-0.6l7.4 2.7 2.7-7.4-7.4-2.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m508.9 30.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m508.5 90.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.1l1.7 0.6 0.7-1.8-1.8-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m508.1 150.9l1-2.8 2.8 1-1 2.8zm0.4-0.2l2.2 0.9 0.8-2.3-2.2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m507.7 211.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.5-0.2l2.7 1 1-2.7-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m507.2 271.3l1.5-4.1 4.1 1.5-1.5 4.1zm0.5-0.3l3.3 1.2 1.2-3.3-3.3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m506.8 331.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m506.4 391.7l1.9-5.3 5.3 1.9-1.9 5.3zm0.7-0.3l4.3 1.5 1.5-4.2-4.2-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m505.9 451.9l2.2-5.9 5.9 2.1-2.1 6zm0.8-0.4l4.7 1.7 1.8-4.7-4.8-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m505.5 512.1l2.4-6.6 6.6 2.4-2.4 6.6zm0.9-0.4l5.2 1.9 1.9-5.3-5.2-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m505.1 572.3l2.6-7.2 7.2 2.6-2.6 7.2zm1-0.5l5.7 2.1 2.1-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m569.2 30.4l0.4-1.2 1.2 0.4-0.4 1.2zm0.1-0.1l0.9 0.3 0.3-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m568.9 90.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.4 0.4-1.2-1.2-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m568.6 150.6l0.8-2 2 0.8-0.8 2zm0.3-0.2l1.6 0.6 0.6-1.6-1.6-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m568.3 210.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.7 0.7-1.9-2-0.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m568 270.9l1.1-2.9 2.9 1.1-1.1 2.9zm0.4-0.2l2.3 0.8 0.9-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m567.7 331.1l1.2-3.4 3.4 1.2-1.2 3.4zm0.4-0.2l2.7 0.9 1-2.6-2.7-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m567.4 391.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3.1 1.2 1.1-3.1-3.1-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m567.1 451.3l1.6-4.2 4.2 1.6-1.6 4.2zm0.6-0.3l3.4 1.3 1.3-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m566.8 511.5l1.7-4.7 4.7 1.7-1.7 4.7zm0.6-0.3l3.8 1.4 1.3-3.8-3.7-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m566.5 571.6l1.9-5.1 5.1 1.9-1.9 5.1zm0.7-0.3l4.1 1.5 1.5-4.1-4.1-1.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-2.9 1.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 1 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-4.4 62l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 2 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-5.8 122.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 3 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-7.3 183.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 4 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-8.8 244.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 5 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-10.2 304.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 6 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-11.7 365.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 7 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-13.2 426.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 8 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-14.6 486.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 9 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-16.1 547.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 10 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m-17.5 608.2l9.3-25.7 25.7 9.3-9.3 25.7zm3.5-1.6l20.6 7.5 7.5-20.6-20.6-7.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 11 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m57.3 1.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 12 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m56 61.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 13 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m54.6 122.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 14 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m53.3 183.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 15 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m52 243.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 16 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m50.6 304.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 17 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m49.3 365l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 18 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m47.9 425.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 19 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m46.6 486.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 20 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m45.3 546.9l7.8-21.6 21.6 7.8-7.8 21.6zm2.9-1.3l17.3 6.3 6.3-17.3-17.3-6.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 21 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m43.9 607.5l8.6-23.6 23.6 8.6-8.6 23.6zm3.2-1.5l18.9 6.8 6.8-18.8-18.8-6.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 22 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m117.6 1.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 23 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m116.3 61.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 24 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m115.1 122.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 25 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m113.9 182.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 26 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m112.7 243.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 27 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m111.5 304l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 28 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m110.3 364.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 29 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m109 425.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 30 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m107.8 485.7l6.5-17.9 17.9 6.5-6.5 17.9zm2.4-1.2l14.3 5.2 5.2-14.2-14.3-5.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 31 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m106.6 546.2l7.2-19.6 19.6 7.2-7.2 19.6zm2.7-1.3l15.7 5.8 5.8-15.7-15.8-5.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 32 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m105.4 606.8l7.8-21.4 21.4 7.8-7.8 21.4zm2.9-1.3l17.2 6.2 6.2-17.2-17.1-6.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 33 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m177.8 1l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 34 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m176.7 61.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 35 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m175.6 122l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 36 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m174.5 182.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 37 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m173.4 243.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 38 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m172.3 303.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 39 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m171.2 364.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 40 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m170.1 424.6l5.3-14.5 14.5 5.3-5.3 14.5zm2-1l11.5 4.3 4.3-11.6-11.6-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 41 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m169 485.1l5.9-16.1 16.1 5.9-5.9 16.1zm2.2-1.1l12.9 4.7 4.6-12.8-12.8-4.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 42 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m167.9 545.6l6.5-17.7 17.7 6.5-6.5 17.7zm2.4-1.2l14.2 5.2 5.1-14.2-14.1-5.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 43 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m166.8 606.1l7.1-19.3 19.3 7.1-7.1 19.3zm2.7-1.3l15.4 5.6 5.6-15.4-15.4-5.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 44 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m238.1 0.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 45 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m237.1 61.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 46 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m236.1 121.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 47 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m235.1 182.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 48 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m234.2 242.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 49 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m233.2 303.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 50 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m232.2 363.6l4.2-11.4 11.4 4.2-4.2 11.4zm1.6-0.7l9.1 3.3 3.4-9.1-9.2-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 51 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m231.2 424.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 52 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m230.3 484.5l5.2-14.2 14.2 5.2-5.2 14.2zm2-0.9l11.4 4.2 4.2-11.4-11.4-4.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 53 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m229.3 545l5.7-15.7 15.7 5.7-5.7 15.7zm2.1-1l12.6 4.6 4.6-12.6-12.6-4.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 54 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m228.3 605.5l6.2-17.2 17.2 6.2-6.2 17.2zm2.3-1.1l13.7 5 5-13.7-13.7-5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 55 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m298.3 0.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 56 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m297.4 61.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 57 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m296.6 121.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 58 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m295.7 182l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 59 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m294.9 242.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 60 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m294 302.8l3.2-8.8 8.8 3.2-3.2 8.8zm1.2-0.6l7 2.6 2.5-7-7-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 61 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m293.2 363.2l3.6-10 10 3.6-3.6 10zm1.3-0.6l8 2.9 3-8-8-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 62 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m292.3 423.6l4.1-11.3 11.3 4.1-4.1 11.3zm1.5-0.7l9 3.2 3.3-9-9-3.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 63 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m291.5 484l4.5-12.5 12.5 4.5-4.5 12.5zm1.7-0.8l10 3.7 3.6-10-10-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 64 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m290.6 544.4l5-13.8 13.8 5-5 13.8zm1.8-0.9l11.1 4 4-11-11-4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 65 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m289.8 604.8l5.4-15 15 5.4-5.4 15zm2-0.9l12 4.3 4.4-12-12-4.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 66 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m358.5 0.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 67 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m357.8 61l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 68 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m357.1 121.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 69 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m356.3 181.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 70 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m355.6 242l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 71 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m354.9 302.4l2.7-7.5 7.5 2.7-2.7 7.5zm1-0.5l6 2.2 2.2-6-6-2.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 72 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m354.2 362.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 73 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m353.4 423.1l3.5-9.7 9.7 3.5-3.5 9.7zm1.3-0.6l7.7 2.8 2.8-7.7-7.7-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 74 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m352.7 483.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 75 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m352 543.7l4.3-11.7 11.7 4.3-4.3 11.7zm1.7-0.7l9.4 3.4 3.4-9.4-9.4-3.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 76 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m351.2 604.1l4.7-12.9 12.9 4.7-4.7 12.9zm1.7-0.8l10.3 3.7 3.8-10.3-10.3-3.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 77 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m418.8 0.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 78 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m418.2 60.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 79 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m417.6 121.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 80 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m417 181.4l1.6-4.4 4.4 1.6-1.6 4.4zm0.6-0.2l3.6 1.3 1.3-3.6-3.6-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 81 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m416.3 241.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 82 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m415.7 302l2.3-6.3 6.3 2.3-2.3 6.3zm0.8-0.4l5 1.8 1.9-5-5-1.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 83 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m415.1 362.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 84 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m414.5 422.6l2.9-8.1 8.1 2.9-2.9 8.1zm1.1-0.5l6.4 2.3 2.3-6.4-6.4-2.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 85 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m413.9 482.8l3.3-8.9 8.9 3.3-3.3 8.9zm1.3-0.6l7.1 2.6 2.6-7.1-7.1-2.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 86 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m413.3 543.1l3.6-9.8 9.8 3.6-3.6 9.8zm1.4-0.6l7.8 2.8 2.9-7.8-7.9-2.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 87 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m412.7 603.4l3.9-10.7 10.7 3.9-3.9 10.7zm1.5-0.7l8.5 3.1 3.2-8.5-8.6-3.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 88 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m479 0.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 89 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m478.5 60.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 90 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m478.1 120.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 91 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m477.6 181.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 92 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m477.1 241.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 93 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m476.6 301.6l1.8-5 5 1.8-1.8 5zm0.7-0.3l4 1.4 1.4-4-4-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 94 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m476.1 361.8l2.1-5.7 5.7 2.1-2.1 5.7zm0.8-0.4l4.6 1.7 1.6-4.6-4.5-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 95 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m475.6 422l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 96 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m475.1 482.3l2.6-7.2 7.2 2.6-2.6 7.2zm0.9-0.5l5.8 2.1 2-5.7-5.7-2.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 97 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m474.6 542.5l2.9-7.9 7.9 2.9-2.9 7.9zm1.1-0.5l6.3 2.2 2.2-6.2-6.2-2.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 98 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m474.2 602.7l3.1-8.5 8.5 3.1-3.1 8.5zm1.2-0.5l6.9 2.5 2.5-6.9-6.9-2.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 99 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m539.3 0.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 100 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m538.9 60.5l0.6-1.6 1.6 0.6-0.6 1.6zm0.2-0.1l1.3 0.5 0.5-1.3-1.3-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 101 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m538.5 120.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.6 0.6-1.7-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 102 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m538.2 180.9l0.9-2.7 2.7 0.9-0.9 2.7zm0.3-0.1l2.2 0.7 0.7-2.1-2.1-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 103 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m537.8 241l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 104 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m537.4 301.2l1.4-3.8 3.8 1.4-1.4 3.8zm0.5-0.3l3 1.1 1.1-3-3-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 105 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m537.1 361.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 106 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m536.7 421.5l1.8-4.8 4.8 1.8-1.8 4.8zm0.7-0.3l3.8 1.4 1.5-3.9-3.9-1.4z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 107 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m536.3 481.7l2-5.4 5.4 2-2 5.4zm0.7-0.4l4.3 1.6 1.6-4.3-4.3-1.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 108 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m536 541.9l2.1-5.9 5.9 2.1-2.1 5.9zm0.8-0.4l4.7 1.8 1.7-4.8-4.7-1.7z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 109 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m535.6 602l2.4-6.4 6.4 2.4-2.4 6.4zm0.9-0.4l5.2 1.8 1.8-5.1-5.1-1.9z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 110 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m599.5 0.2l0.3-0.7 0.7 0.3-0.3 0.7zm0.1-0.1l0.6 0.2 0.2-0.5-0.6-0.2z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 111 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m599.3 60.3l0.4-1 1 0.4-0.4 1zm0.2 0l0.8 0.3 0.4-0.9-0.9-0.3z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 112 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m599 120.5l0.5-1.5 1.5 0.5-0.5 1.5zm0.1-0.1l1.2 0.4 0.4-1.1-1.1-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 113 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m598.8 180.6l0.6-1.8 1.8 0.6-0.6 1.8zm0.2-0.1l1.4 0.5 0.6-1.4-1.5-0.5z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 114 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m598.5 240.7l0.8-2.2 2.2 0.8-0.8 2.2zm0.3-0.2l1.7 0.7 0.6-1.8-1.7-0.6z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 115 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m598.3 300.8l0.9-2.5 2.5 0.9-0.9 2.5zm0.3-0.2l2 0.8 0.8-2-2-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 116 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m598.1 360.9l1-2.8 2.8 1-1 2.8zm0.4-0.1l2.3 0.8 0.8-2.3-2.3-0.8z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 117 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m597.8 421l1.2-3.2 3.2 1.2-1.2 3.2zm0.5-0.2l2.5 0.9 1-2.5-2.6-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 118 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m597.6 481.1l1.3-3.5 3.5 1.3-1.3 3.5zm0.5-0.2l2.9 1 1-2.8-2.8-1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 119 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m597.3 541.2l1.5-3.9 3.9 1.5-1.5 3.9zm0.6-0.3l3.1 1.2 1.2-3.2-3.2-1.1z" />
                  </g>
                </g>
                <g id="&lt;Group&gt;">
                  <g id="shape other 10 1 120 ">
                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" class="fill-yellow-500/20"
                      d="m597.1 601.4l1.5-4.3 4.3 1.5-1.5 4.3zm0.5-0.2l3.5 1.2 1.2-3.4-3.4-1.3z" />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div class="relative card-body">
      <h6 class="uppercase text-14">Enterprise Plan</h6>
      <div class="flex items-center justify-center h-32">
        <div>
          <lucide-angular name="TrendingUp" class="mx-auto mb-2 text-green-500 size-7"></lucide-angular>
          <h3>+9.6%</h3>
        </div>
      </div>
      <h6>
        <lucide-angular name="UserRound" class="inline-block ltr:mr-1 rtl:ml-1 size-4 mb-0.5"></lucide-angular> 2647
      </h6>
    </div>
  </div>
</div>
