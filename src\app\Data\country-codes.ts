export const countryCodes = [
  {
    name: 'Ghana',
    dial_code: '+233',
    code: 'gh',
    format: '020 123 4567',
    mask: '000-000-0000',
  },
  {
    name: 'Greece',
    dial_code: '+30',
    code: 'gr',
    format: '(690-123-4567)',
    mask: '000-000-0000',
  },
  {
    name: 'Grenada',
    dial_code: '+1 473',
    code: 'gd',
    format: '(473) 403-1234',
    mask: '(000) 000-0000',
  },
  {
    name: 'Guatemala',
    dial_code: '+502',
    code: 'gt',
    format: '5123 4567',
    mask: '0000 0000',
  },
  {
    name: 'Guinea',
    dial_code: '+224',
    code: 'gn',
    format: '601 12 34 56',
    mask: '000 00 00 00',
  },
  {
    name: 'Guinea-Bissau',
    dial_code: '+245',
    code: 'gw',
    format: '955 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Guyana',
    dial_code: '+592',
    code: 'gy',
    format: '609 1234',
    mask: '0000 0000',
  },
  {
    name: 'Haiti',
    dial_code: '+509',
    code: 'ht',
    format: '34 56 7890',
    mask: '00 00 0000',
  },
  {
    name: 'Honduras',
    dial_code: '+504',
    code: 'hn',
    format: '9123-4567',
    mask: '0000-0000',
  },
  {
    name: 'Hungary',
    dial_code: '+36',
    code: 'hu',
    format: '20 123 4567',
    mask: '20 000 0000',
  },
  {
    name: 'Iceland',
    dial_code: '+354',
    code: 'is',
    format: '611 2345',
    mask: '000 0000',
  },
  {
    name: 'India',
    dial_code: '+91',
    code: 'in',
    format: '98765 43210',
    mask: '00000 00000',
  },
  {
    name: 'Indonesia',
    dial_code: '+62',
    code: 'id',
    format: '0812-345-6789',
    mask: '0000-000-0000',
  },
  {
    name: 'Iran',
    dial_code: '+98',
    code: 'ir',
    format: '0912 345 6789',
    mask: '0000 000 0000',
  },
  {
    name: 'Iraq',
    dial_code: '+964',
    code: 'iq',
    format: '0791 234 5678',
    mask: '0000 000 0000',
  },
  {
    name: 'Ireland',
    dial_code: '+353',
    code: 'ie',
    format: '087 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Israel',
    dial_code: '+972',
    code: 'il',
    format: '052 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Italy',
    dial_code: '+39',
    code: 'it',
    format: '312 345 6789',
    mask: '000 000 0000',
  },
  {
    name: 'Jamaica',
    dial_code: '+1 876',
    code: 'jm',
    format: '(876) 210-1234',
    mask: '(000) 000-0000',
  },
  {
    name: 'Japan',
    dial_code: '+81',
    code: 'jp',
    format: '090-1234-5678',
    mask: '0000-0000-0000',
  },
  {
    name: 'Jordan',
    dial_code: '+962',
    code: 'jo',
    format: '07 9012 3456',
    mask: '0x 0000 0000',
  },
  {
    name: 'Kazakhstan',
    dial_code: '+7',
    code: 'kz',
    format: '771 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Kenya',
    dial_code: '+254',
    code: 'ke',
    format: '0712 345678',
    mask: '0000 000000',
  },
  {
    name: 'Kiribati',
    dial_code: '+686',
    code: 'ki',
    format: '72012345',
    mask: '00000000',
  },
  {
    name: 'Kuwait',
    dial_code: '+965',
    code: 'kw',
    format: '5001 2345',
    mask: '0000 0000',
  },
  {
    name: 'Kyrgyzstan',
    dial_code: '+996',
    code: 'kg',
    format: '0700 123 456',
    mask: '0000 000 000',
  },
  {
    name: 'Laos',
    dial_code: '+856',
    code: 'la',
    format: '020 23 456 789',
    mask: '000 00 000 000',
  },
  {
    name: 'Latvia',
    dial_code: '+371',
    code: 'lv',
    format: '26 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Lebanon',
    dial_code: '+961',
    code: 'lb',
    format: '71 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Lesotho',
    dial_code: '+266',
    code: 'ls',
    format: '5012 3456',
    mask: '0000 0000',
  },
  {
    name: 'Liberia',
    dial_code: '+231',
    code: 'lr',
    format: '077 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Libya',
    dial_code: '+218',
    code: 'ly',
    format: '091 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Liechtenstein',
    dial_code: '+423',
    code: 'li',
    format: '660 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Lithuania',
    dial_code: '+370',
    code: 'lt',
    format: '612 34567',
    mask: '000 00000',
  },
  {
    name: 'Luxembourg',
    dial_code: '+352',
    code: 'lu',
    format: '621 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Madagascar',
    dial_code: '+261',
    code: 'mg',
    format: '032 12 345 67',
    mask: '000 00 000 00',
  },
  {
    name: 'Malawi',
    dial_code: '+265',
    code: 'mw',
    format: '0991 23 45 67',
    mask: '0000 00 00 00',
  },
  {
    name: 'Malaysia',
    dial_code: '+60',
    code: 'my',
    format: '012-345 6789',
    mask: '000-000 0000',
  },
  {
    name: 'Maldives',
    dial_code: '+960',
    code: 'mv',
    format: '771-2345',
    mask: '000-0000',
  },
  {
    name: 'Mali',
    dial_code: '+223',
    code: 'ml',
    format: '65 01 23 45',
    mask: '00 00 00 00',
  },
  {
    name: 'Malta',
    dial_code: '+356',
    code: 'mt',
    format: '9696 1234',
    mask: '0000 0000',
  },
  {
    name: 'Marshall Islands',
    dial_code: '+692',
    code: 'mh',
    format: '235 1234',
    mask: '000 0000',
  },
  {
    name: 'Mauritania',
    dial_code: '+222',
    code: 'mr',
    format: '22 12 34 56',
    mask: '00 00 00 00',
  },
  {
    name: 'Mauritius',
    dial_code: '+230',
    code: 'mu',
    format: '5 25 25 25',
    mask: '0 00 00 00',
  },
  {
    name: 'Mexico',
    dial_code: '+52',
    code: 'mx',
    format: '55 1234 5678',
    mask: '00 0000 0000',
  },
  {
    name: 'Micronesia',
    dial_code: '+691',
    code: 'fm',
    format: '320 1234',
    mask: '000 0000',
  },
  {
    name: 'Moldova',
    dial_code: '+373',
    code: 'md',
    format: '079 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Monaco',
    dial_code: '+377',
    code: 'mc',
    format: '06 12 34 56',
    mask: '00 00 00 00',
  },
  {
    name: 'Mongolia',
    dial_code: '+976',
    code: 'mn',
    format: '9900 0000',
    mask: '0000 0000',
  },
  {
    name: 'Montenegro',
    dial_code: '+382',
    code: 'me',
    format: '67 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Morocco',
    dial_code: '+212',
    code: 'ma',
    format: '061 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Mozambique',
    dial_code: '+258',
    code: 'mz',
    format: '82 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'Myanmar',
    dial_code: '+95',
    code: 'mm',
    format: '09-123-4567',
    mask: '00-000-0000',
  },
  {
    name: 'Namibia',
    dial_code: '+264',
    code: 'na',
    format: '081 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Nauru',
    dial_code: '+674',
    code: 'nr',
    format: '555 1234',
    mask: '000 0000',
  },
  {
    name: 'Nepal',
    dial_code: '+977',
    code: 'np',
    format: '981-234-5678',
    mask: '000-000-0000',
  },
  {
    name: 'Netherlands',
    dial_code: '+31',
    code: 'nl',
    format: '06 12345678',
    mask: '00 00000000',
  },
  {
    name: 'New Caledonia',
    dial_code: '+687',
    code: 'nc',
    format: '82 12 34',
    mask: '00 00 00',
  },
  {
    name: 'New Zealand',
    dial_code: '+64',
    code: 'nz',
    format: '021 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Nicaragua',
    dial_code: '+505',
    code: 'ni',
    format: '8888-8888',
    mask: '0000-0000',
  },
  {
    name: 'Niger',
    dial_code: '+227',
    code: 'ne',
    format: '20 73 45 67',
    mask: '00 00 00 00',
  },
  {
    name: 'Nigeria',
    dial_code: '+234',
    code: 'ng',
    format: '080 1234 5678',
    mask: '000 0000 0000',
  },
  {
    name: 'Niue',
    dial_code: '+683',
    code: 'nu',
    format: '123 4567',
    mask: '000 0000',
  },
  {
    name: 'North Macedonia',
    dial_code: '+389',
    code: 'mk',
    format: '078 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Norway',
    dial_code: '+47',
    code: 'no',
    format: '900 12 345',
    mask: '000 00 000',
  },
  {
    name: 'Oman',
    dial_code: '+968',
    code: 'om',
    format: '9512 3456',
    mask: '0000 0000',
  },
  {
    name: 'Pakistan',
    dial_code: '+92',
    code: 'pk',
    format: '0300 1234567',
    mask: '0000 0000000',
  },
  {
    name: 'Palau',
    dial_code: '+680',
    code: 'pw',
    format: '777 1234',
    mask: '000 0000',
  },
  {
    name: 'Palestine',
    dial_code: '+970',
    code: 'ps',
    format: '059 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Panama',
    dial_code: '+507',
    code: 'pa',
    format: '6000-1234',
    mask: '0000-0000',
  },
  {
    name: 'Papua New Guinea',
    dial_code: '+675',
    code: 'pg',
    format: '7123 4567',
    mask: '0000 0000',
  },
  {
    name: 'Paraguay',
    dial_code: '+595',
    code: 'py',
    format: '0981 234 567',
    mask: '000 000 000',
  },
  {
    name: 'Peru',
    dial_code: '+51',
    code: 'pe',
    format: '987 654 321',
    mask: '000 000 000',
  },
  {
    name: 'Philippines',
    dial_code: '+63',
    code: 'ph',
    format: '0917 123 4567',
    mask: '0000 000 0000',
  },
  {
    name: 'Poland',
    dial_code: '+48',
    code: 'pl',
    format: '600 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Portugal',
    dial_code: '+351',
    code: 'pt',
    format: '912 345 678',
    mask: '000 000 000',
  },
  {
    name: 'Qatar',
    dial_code: '+974',
    code: 'qa',
    format: '5555 5555',
    mask: '0000 0000',
  },
  {
    name: 'Romania',
    dial_code: '+40',
    code: 'ro',
    format: '0712 345 678',
    mask: '0000 000 000',
  },
  {
    name: 'Russia',
    dial_code: '+7',
    code: 'ru',
    format: '912 345 6789',
    mask: '000 000 0000',
  },
  {
    name: 'Rwanda',
    dial_code: '+250',
    code: 'rw',
    format: '078 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Saint Kitts and Nevis',
    dial_code: '+1 869',
    code: 'kn',
    format: '(869) 765-4321',
    mask: '(000) 000-0000',
  },
  {
    name: 'Saint Lucia',
    dial_code: '+1 758',
    code: 'lc',
    format: '(758) 123-4567',
    mask: '(000) 000-0000',
  },
  {
    name: 'Saint Vincent and the Grenadines',
    dial_code: '+1 784',
    code: 'vc',
    format: '(784) 123-4567',
    mask: '(000) 000-0000',
  },
  {
    name: 'Samoa',
    dial_code: '+685',
    code: 'ws',
    format: '770 1234',
    mask: '000 0000',
  },
  {
    name: 'San Marino',
    dial_code: '+378',
    code: 'sm',
    format: '0549 999 999',
    mask: '000 000 000',
  },
  {
    name: 'Sao Tome and Principe',
    dial_code: '+239',
    code: 'st',
    format: '991 2344',
    mask: '000 0000',
  },
  {
    name: 'Saudi Arabia',
    dial_code: '+966',
    code: 'sa',
    format: '050 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Senegal',
    dial_code: '+221',
    code: 'sn',
    format: '77 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'Serbia',
    dial_code: '+381',
    code: 'rs',
    format: '60 1234567',
    mask: '00 0000000',
  },
  {
    name: 'Seychelles',
    dial_code: '+248',
    code: 'sc',
    format: '2 123 4567',
    mask: '0 000 0000',
  },
  {
    name: 'Sierra Leone',
    dial_code: '+232',
    code: 'sl',
    format: '76 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Singapore',
    dial_code: '+65',
    code: 'sg',
    format: '9123 4567',
    mask: '0000 0000',
  },
  {
    name: 'Sint Maarten',
    dial_code: '+1 721',
    code: 'sx',
    format: '(721) 123-4567',
    mask: '(000) 000-0000',
  },
  {
    name: 'Slovakia',
    dial_code: '+421',
    code: 'sk',
    format: '0917 123 456',
    mask: '0000 000 000',
  },
  {
    name: 'Slovenia',
    dial_code: '+386',
    code: 'si',
    format: '031 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Solomon Islands',
    dial_code: '+677',
    code: 'sb',
    format: '12345',
    mask: '00000',
  },
  {
    name: 'Somalia',
    dial_code: '+252',
    code: 'so',
    format: '07 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'South Africa',
    dial_code: '+27',
    code: 'za',
    format: '083 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'South Sudan',
    dial_code: '+211',
    code: 'ss',
    format: '091 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Spain',
    dial_code: '+34',
    code: 'es',
    format: '612 345 678',
    mask: '000 000 000',
  },
  {
    name: 'Sri Lanka',
    dial_code: '+94',
    code: 'lk',
    format: '077 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Sudan',
    dial_code: '+249',
    code: 'sd',
    format: '091 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Suriname',
    dial_code: '+597',
    code: 'sr',
    format: '877 1234',
    mask: '000 0000',
  },
  {
    name: 'Sweden',
    dial_code: '+46',
    code: 'se',
    format: '070 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Switzerland',
    dial_code: '+41',
    code: 'ch',
    format: '076 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Syria',
    dial_code: '+963',
    code: 'sy',
    format: '09 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'Taiwan',
    dial_code: '+886',
    code: 'tw',
    format: '0912 345 678',
    mask: '0000 000 000',
  },
  {
    name: 'Tajikistan',
    dial_code: '+992',
    code: 'tj',
    format: '91 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'Tanzania',
    dial_code: '+255',
    code: 'tz',
    format: '071 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Thailand',
    dial_code: '+66',
    code: 'th',
    format: '081 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Timor-Leste',
    dial_code: '+670',
    code: 'tl',
    format: '773 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Togo',
    dial_code: '+228',
    code: 'tg',
    format: '90 12 34 56',
    mask: '00 00 00 00',
  },
  {
    name: 'Tonga',
    dial_code: '+676',
    code: 'to',
    format: '12345',
    mask: '00000',
  },
  {
    name: 'Trinidad and Tobago',
    dial_code: '+1 868',
    code: 'tt',
    format: '(868) 123-4567',
    mask: '(000) 000-0000',
  },
  {
    name: 'Tunisia',
    dial_code: '+216',
    code: 'tn',
    format: '20 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Turkey',
    dial_code: '+90',
    code: 'tr',
    format: '0505 123 4567',
    mask: '0000 000 0000',
  },
  {
    name: 'Turkmenistan',
    dial_code: '+993',
    code: 'tm',
    format: '65 123 456',
    mask: '00 000 000',
  },
  {
    name: 'Tuvalu',
    dial_code: '+688',
    code: 'tv',
    format: '1234',
    mask: '0000',
  },
  {
    name: 'Uganda',
    dial_code: '+256',
    code: 'ug',
    format: '077 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Ukraine',
    dial_code: '+380',
    code: 'ua',
    format: '050 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'United Arab Emirates',
    dial_code: '+971',
    code: 'ae',
    format: '50 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'United Kingdom',
    dial_code: '+44',
    code: 'gb',
    format: '07123 456 789',
    mask: '00000 000000',
  },
  {
    name: 'United States',
    dial_code: '+1',
    code: 'us',
    format: '(123) 456-7890',
    mask: '(000) 000-0000',
  },
  {
    name: 'Uruguay',
    dial_code: '+598',
    code: 'uy',
    format: '094 123 456',
    mask: '000 000 000',
  },
  {
    name: 'Uzbekistan',
    dial_code: '+998',
    code: 'uz',
    format: '91 123 4567',
    mask: '00 000 0000',
  },
  {
    name: 'Vanuatu',
    dial_code: '+678',
    code: 'vu',
    format: '12345',
    mask: '00000',
  },
  {
    name: 'Vatican City',
    dial_code: '+379',
    code: 'va',
    format: '333 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Venezuela',
    dial_code: '+58',
    code: 've',
    format: '0412-123-4567',
    mask: '0000-000-0000',
  },
  {
    name: 'Vietnam',
    dial_code: '+84',
    code: 'vn',
    format: '091 234 5678',
    mask: '000 000 0000',
  },
  {
    name: 'Yemen',
    dial_code: '+967',
    code: 'ye',
    format: '01 234 5678',
    mask: '00 000 0000',
  },
  {
    name: 'Zambia',
    dial_code: '+260',
    code: 'zm',
    format: '097 123 4567',
    mask: '000 000 0000',
  },
  {
    name: 'Zimbabwe',
    dial_code: '+263',
    code: 'zw',
    format: '077 123 4567',
    mask: '000 000 0000',
  },
];
