<app-page-title [Title]="'List View'" [pageTitle]="'Invoice'"></app-page-title>

<div>
  <div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-2 card">
      <div class="card-body">
        <div class="flex items-center justify-center mx-auto mb-4 size-16 bg-gradient-to-t from-green-500/10 rounded-modern">
          <lucide-angular name="CircleCheckBig" class="relative text-green-500 stroke-1 size-9 fill-green-500/10">
          </lucide-angular>
        </div>
        <h5 class="mb-1">16</h5>
        <p class="mb-4">Paid Invoice</p>
        <p class="text-gray-500 dark:text-dark-500"><span class="align-bottom badge badge-green"><span>35.56</span>%</span> This month</p>
      </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-2 card">
      <div class="card-body">
        <div class="flex items-center justify-center mx-auto mb-4 size-16 bg-gradient-to-t from-pink-500/10 rounded-modern">
          <lucide-angular name="CircleAlert" class="relative text-pink-500 stroke-1 size-9 fill-pink-500/10">
          </lucide-angular>
        </div>
        <h5 class="mb-1">8</h5>
        <p class="mb-4">Unpaid Invoice</p>
        <p class="text-gray-500 dark:text-dark-500"><span class="align-bottom badge badge-green"><span>17.78</span>%</span> This month</p>
      </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-2 card">
      <div class="card-body">
        <div class="flex items-center justify-center mx-auto mb-4 size-16 bg-gradient-to-t from-yellow-500/10 rounded-modern">
          <lucide-angular name="hourglass" class="relative text-yellow-500 stroke-1 size-9 fill-yellow-500/10">
          </lucide-angular>
        </div>
        <h5 class="mb-1">12</h5>
        <p class="mb-4">Pending Invoice</p>
        <p class="text-gray-500 dark:text-dark-500"><span class="align-bottom badge badge-green"><span>26.67</span>%</span> This month</p>
      </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-2 card">
      <div class="card-body">
        <div class="flex items-center justify-center mx-auto mb-4 size-16 bg-gradient-to-t from-red-500/10 rounded-modern">
          <lucide-angular name="x" class="relative text-red-500 stroke-1 size-9 fill-red-500/10"></lucide-angular>
        </div>
        <h5 class="mb-1">9</h5>
        <p class="mb-4">Overdue Invoice</p>
        <p class="text-gray-500 dark:text-dark-500"><span class="align-bottom badge badge-green"><span>20.00</span>%</span> This month</p>
      </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-4 card">
      <div class="flex items-center gap-3 card-header">
        <h6 class="card-title grow">Invoice Status</h6>
        <div class="dropdown">
          <button type="button" class="flex px-2 py-1 text-xs border-gray-200 dark:border-dark-800 link link-red btn" domixDropdownToggle [dropdownMenu]="dropdown1">
            Last Week
            <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <div class="!fixed p-2 dropdown-menu dropdown-right" #dropdown1>
            <a class="dropdown-item">
              Last Week
            </a>

            <a class="dropdown-item">
              Last Month
            </a>
            <a class="dropdown-item">
              Last Years
            </a>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div dir="ltr">
          <app-domix-charts [series]="donutChart.series" [chartOptions]="donutChart.chartOptions">
          </app-domix-charts>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="justify-between card-header md:flex">
      <div>
        <h6 class="mb-1">All Invoices</h6>
        <p class="text-gray-500 dark:text-dark-500">Manage your invoice</p>
      </div>
      <div>
        <div class="flex items-center gap-5 mt-3 md:mt-0">
          <div class="relative group/form grow">
            <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search invoice ..." [(ngModel)]="searchText" (ngModelChange)="updateDisplayedData()">
            <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-none">
              <lucide-angular name="search" class="text-gray-500 dark:text-dark-500 size-4 fill-gray-100 dark:fill-dark-850"></lucide-angular>
            </button>
          </div>
          <a class="btn btn-primary shrink-0" routerLink="/apps-invoice-create">Create Invoice</a>
          <button class="btn btn-red btn-icon" *ngIf="checkedRows.length" (click)="deleteSelectedItem()">
            <lucide-angular name="trash" class="inline-block size-4"></lucide-angular>
          </button>
        </div>
      </div>
    </div>


    <div class="overflow-x-auto">
      <table class="table border-separate hovered flush border-spacing-y-2 whitespace-nowrap">
        <tbody>
          <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
            <th *ngFor="let col of columnDefs" class="!font-medium cursor-pointer">
              <div class="flex items-center gap-space">
                <div class="!font-medium" *ngIf="col.headerCheckbox">
                  <div class="input-check-group">
                    <label for="checkboxAll" class="hidden input-check-label"></label>
                    <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" [(ngModel)]="headerCheckBox" (change)="headerCheckBoxChange()" />
                  </div>
                </div>
                <div (click)="onSortChange(col)">
                  {{col.headerName}} <span *ngIf="col.sortable">
                    {{ col.sortDiraction === 'asc' ? '↑' : '↓' }}</span>
                </div>
              </div>
            </th>
          </tr>
          <ng-container *ngIf="displayedData.length > 0">
            <ng-container *ngFor="let invoice of displayedData; let i = index">
              <tr>
                <td>
                  <div class="flex items-center gap-space">
                    <div class="!font-medium" *ngIf="hasHeaderCheckbox">
                      <div class="input-check-group">
                        <label for="checkboxAll" class="hidden input-check-label"></label>
                        <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" [checked]="invoice.checked" (change)="onRowCheckboxChange(i)" />
                      </div>
                    </div>
                    <a href="javascript: void(0);" class="link link-primary">{{ invoice.invoicesID }}</a>
                  </div>
                </td>
                <td>
                  <div class="flex items-center gap-2">
                    <img [src]="invoice.image" alt="" class="rounded-full shrink-0 size-7">
                    <a class="text-current link link-primary grow">{{invoice.clientName}}</a>
                  </div>
                </td>
                <td>{{invoice.country}}</td>
                <td>{{invoice.invoiceDate}}</td>
                <td>{{invoice.dueDate}}</td>
                <td>{{invoice.amount}}</td>
                <td>
                  <span class="badge" [ngClass]="{
                                  'badge-green': invoice.status === 'Paid',
                                  'badge-pink': invoice.status === 'Unpaid',
                                  'badge-yellow': invoice.status === 'Pending',
                                  'badge-red': invoice.status === 'Overdue'
                              }">{{invoice.status}}</span>
                </td>
                <td>
                  <div class="dropdown" domixDropdownToggle [dropdownMenu]="dropdown2">
                    <button type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                      <i class="ri-more-2-fill"></i>
                    </button>
                    <div #dropdown2 class="!fixed z-50 p-2 dropdown-menu">
                      <ul>
                        <li>
                          <a class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                          </a>
                        </li>
                        <li>
                          <a class="dropdown-item" (click)="editinvoice(invoice.invoicesID)">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                          </a>
                        </li>
                        <li>
                          <a class="dropdown-item" (click)="deleteModal(invoice.invoicesID)">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
            </ng-container>
          </ng-container>
          <tr>
            <ng-container *ngIf="displayedData.length == 0">
              <td colspan="10" class="!p-8">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                  <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#60e8fe"></stop>
                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                    <stop offset=".197" stop-color="#97f0fe"></stop>
                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                    <stop offset=".525" stop-color="#dafaff"></stop>
                    <stop offset=".687" stop-color="#eefdff"></stop>
                    <stop offset=".846" stop-color="#fbfeff"></stop>
                    <stop offset="1" stop-color="#fff"></stop>
                  </linearGradient>
                  <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                  </path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                  </path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                  </path>
                </svg>
                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
              </td>
            </ng-container>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <app-domix-pagination [currentPage]="currentPage" [totalPages]="totalPages" [showingStart]="showingStart" [showingEnd]="showingEnd" [totalResults]="gridData.length" (pageChanged)="onPageChange($event)">
  </app-domix-pagination>

</div>