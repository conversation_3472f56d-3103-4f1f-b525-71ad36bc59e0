<app-page-title [pageTitle]="'Ecommerce'" [Title]="'Category List'"></app-page-title>

<div class="grid grid-cols-12 gap-5">
    <div class="col-span-12 lg:col-span-7 xl:col-span-8">
        <div class="card">
            <div class="card-header">
                <div class="grid items-center grid-cols-12 gap-3">
                    <div class="col-span-12 md:col-span-3">
                        <h6 class="card-title">Category List</h6>
                    </div>

                    <div class="col-span-12 md:col-span-4 md:col-start-9">
                        <div class="flex gap-2">
                            <div class="relative group/form grow">
                                <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." [(ngModel)]="searchText" (ngModelChange)="updateDisplayedData()">
                                <button (click)="addEditCat(null,null)" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-none">
                                    <lucide-angular name="search" class="size-4"></lucide-angular>
                                </button>
                            </div>
                            <button *ngIf="checkedRows.length" class="btn btn-red btn-icon shrink-0" (click)="deleteSelectedItem()"><lucide-angular name="trash" class="size-4"></lucide-angular></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pt-0 card-body">
                <div class="overflow-x-auto table-box">
                    <table class="table hovered">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th *ngFor="let col of defs" class="!font-medium cursor-pointer">
                                    <div class="flex items-center gap-space">
                                        <div class="!font-medium" *ngIf="col.headerCheckbox">
                                            <div class="input-check-group">
                                                <label for="checkboxAll" class="hidden input-check-label"></label>
                                                <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" [(ngModel)]="headerCheckBox" (change)="headerCheckBoxChange()" />
                                            </div>
                                        </div>
                                        <div (click)="onSortChange(col)">
                                            {{col.headerName}} <span *ngIf="col.sortable">
                                                {{ col.sortDiraction === 'asc' ? '↑' : '↓' }}</span>
                                        </div>
                                    </div>
                                </th>
                                <!-- <th class="!font-medium input-check-group">
                                    <label for="checkboxAll" class="hidden input-check-label"></label>
                                    <input id="checkboxAll" class="whitespace-nowrap input-check input-check-primary"
                                        type="checkbox" [(ngModel)]="headerCheckBox"
                                        (change)="headerCheckBoxChange()" />
                                </th>
                                <th *ngFor="let col of defs" (click)="onSortChange(col)"
                                    class="whitespace-nowrap !font-medium cursor-pointer">
                                    {{col.headerName}}
                                    <span *ngIf="col.sortable">{{ col.sortDiraction === 'asc' ? '↑' : '↓' }}</span>
                                </th> -->
                            </tr>
                            <ng-container *ngIf="displayedData.length">
                                <ng-container *ngFor="let product of displayedData;let i = index">
                                    <tr>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <div class="input-check-group">
                                                    <label class="hidden input-check-label"></label>
                                                    <input class="input-check input-check-primary" type="checkbox" [checked]="product.checked" (change)="onRowCheckboxChange(i)" />
                                                </div>
                                                <div>
                                                    <a class="link link-primary">{{product.productID}}</a>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <div class="flex items-center justify-center p-1 border border-gray-200 rounded size-9 dark:border-dark-800 shrink-0">
                                                    <img [src]="product.image" alt="" class="rounded">
                                                </div>
                                                <h6><a href="javascript: void(0);">{{product.category}}</a></h6>
                                            </div>
                                        </td>
                                        <td>{{product.products}}</td>
                                        <td>
                                            <span class="badge" [ngClass]="{
                                                'badge-green': product.status,
                                                'badge-gray': !product.status
                                            }">{{product.status ? 'Active' : 'Inactive'}}</span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button type="button" class="flex items-center text-gray-500 dark:text-dark-500" domixDropdownToggle [dropdownMenu]="temp">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div class="!fixed p-2 dropdown-menu" #temp>
                                                    <ul>
                                                        <li>
                                                            <a class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i>
                                                                <span>Overview</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" (click)="addEditCat(product,i)">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i>
                                                                <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" (click)="del(i)">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i>
                                                                <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-container>
                            </ng-container>
                            <tr>
                                <ng-container *ngIf="!displayedData.length">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0l-4.331-4.331">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                            </path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records
                                            found</p>
                                    </td>
                                </ng-container>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <app-domix-pagination [currentPage]="currentPage" [totalPages]="totalPages" [showingStart]="showingStart" [showingEnd]="showingEnd" [totalResults]="gridData.length" (pageChanged)="onPageChange($event)">
                </app-domix-pagination>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-5 xl:col-span-4">
        <div class="sticky top-24 card" [formGroup]="formGroup" *ngIf="formGroup">
            <div class="card-header">
                <h6 class="card-title">Add New Category</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <div>
                            <label for="logo" class="flex items-center justify-center p-2 mx-auto overflow-hidden bg-gray-100 border border-gray-200 rounded cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-32">
                                <img *ngIf="imageUrl" [src]="imageUrl" class="object-cover w-full h-full">
                                <span *ngIf="!imageUrl" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <lucide-angular name="upload"></lucide-angular>
                                    <span class="block mt-2">Upload Images</span>
                                </span>
                            </label>
                            <span *ngIf="!imageUrl" class="text-red-500">This Field is required</span>

                            <div class="hidden mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo test</span>
                                    <input #fileInput type="file" name="logo" id="logo" (change)="onFileChange($event, fileInput)" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12">
                        <label for="categoryNameInput" class="form-label">Category Name</label>
                        <input type="text" id="categoryNameInput" class="form-input" placeholder="Category name" formControlName="category">
                        <span *ngIf="formGroup.get('category')?.hasError('required')" class="text-red-500">This Field is
                            required</span>
                    </div>
                    <div class="col-span-12">
                        <label for="categoryNameInput" class="form-label">Product</label>
                        <input class="form-input" placeholder="Enter product" formControlName="products">
                        <span *ngIf="formGroup.get('products')?.hasError('required')" class="text-red-500">This Field is
                            required</span>
                    </div>
                    <div class="col-span-12">
                        <label for="descriptionTextarea" class="form-label">Description</label>
                        <textarea name="descriptionTextarea" id="descriptionTextarea" rows="3" class="h-auto form-input" placeholder="Enter your description" formControlName="description"></textarea>
                        <span *ngIf="formGroup.get('description')?.hasError('required')" class="text-red-500">This Field
                            is required</span>
                    </div>
                    <div class="col-span-12">
                        <label for="statusSelect" class="form-label">Status</label>
                        <select id="statusSelect" class="form-input" formControlName="status">
                            <option [value]="true">Active</option>
                            <option [value]="false">Inactive</option>
                        </select>
                        <span *ngIf="formGroup.get('status')?.hasError('required')" class="text-red-500">This Field is
                            required</span>
                    </div>
                    <div class="flex items-center justify-end col-span-12 gap-2">
                        <button class="btn btn-sub-gray" (click)="reset()"><lucide-angular name="rotate-ccw" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></lucide-angular> <span class="align-middle">Reset</span></button>
                        <button class="btn btn-primary" (click)="addOrEditedData()"><lucide-angular name="plus" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></lucide-angular> <span class="align-middle">Add Category</span></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- <app-cat-add-edit [data]="editData" (addOrEditedData)="addOrEditedData($event)"></app-cat-add-edit> -->
    </div>
</div>