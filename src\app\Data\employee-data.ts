export const EMPLOYEES = [
  {
    Name: '<PERSON>',
    Position: 'System Architect',
    Office: 'Edinburgh',
    Age: 61,
    'Start date': '2011-04-25',
    Salary: '$320,800',
  },
  {
    Name: '<PERSON>',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 63,
    'Start date': '2011-07-25',
    Salary: '$170,750',
  },
  {
    Name: '<PERSON>',
    Position: 'Junior Technical Author',
    Office: 'San Francisco',
    Age: 66,
    'Start date': '2009-01-12',
    Salary: '$86,000',
  },
  {
    Name: '<PERSON><PERSON>',
    Position: 'Senior Javascript Developer',
    Office: 'Edinburgh',
    Age: 22,
    'Start date': '2012-03-29',
    Salary: '$433,060',
  },
  {
    Name: '<PERSON><PERSON>',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 33,
    'Start date': '2008-11-28',
    Salary: '$162,700',
  },
  {
    Name: '<PERSON><PERSON><PERSON>',
    Position: 'Integration Specialist',
    Office: 'New York',
    Age: 61,
    'Start date': '2012-12-02',
    Salary: '$372,000',
  },
  {
    Name: '<PERSON><PERSON>',
    Position: 'Sales Assistant',
    Office: 'San Francisco',
    Age: 59,
    'Start date': '2012-08-06',
    Salary: '$137,500',
  },
  {
    Name: 'Rhona Davidson',
    Position: 'Integration Specialist',
    Office: 'Tokyo',
    Age: 55,
    'Start date': '2010-10-14',
    Salary: '$327,900',
  },
  {
    Name: 'Colleen Hurst',
    Position: 'Javascript Developer',
    Office: 'San Francisco',
    Age: 39,
    'Start date': '2009-09-15',
    Salary: '$205,500',
  },
  {
    Name: 'Tiger Nixon',
    Position: 'System Architect',
    Office: 'Edinburgh',
    Age: 61,
    'Start date': '2011-04-25',
    Salary: '$320,800',
  },
  {
    Name: 'Garrett Winters',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 63,
    'Start date': '2011-07-25',
    Salary: '$170,750',
  },
  {
    Name: 'Ashton Cox',
    Position: 'Junior Technical Author',
    Office: 'San Francisco',
    Age: 66,
    'Start date': '2009-01-12',
    Salary: '$86,000',
  },
  {
    Name: 'Cedric Kelly',
    Position: 'Senior Javascript Developer',
    Office: 'Edinburgh',
    Age: 22,
    'Start date': '2012-03-29',
    Salary: '$433,060',
  },
  {
    Name: 'Airi Satou',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 33,
    'Start date': '2008-11-28',
    Salary: '$162,700',
  },
  {
    Name: 'Brielle Williamson',
    Position: 'Integration Specialist',
    Office: 'New York',
    Age: 61,
    'Start date': '2012-12-02',
    Salary: '$372,000',
  },
  {
    Name: 'Herrod Chandler',
    Position: 'Sales Assistant',
    Office: 'San Francisco',
    Age: 59,
    'Start date': '2012-08-06',
    Salary: '$137,500',
  },
  {
    Name: 'Rhona Davidson',
    Position: 'Integration Specialist',
    Office: 'Tokyo',
    Age: 55,
    'Start date': '2010-10-14',
    Salary: '$327,900',
  },
  {
    Name: 'Colleen Hurst',
    Position: 'Javascript Developer',
    Office: 'San Francisco',
    Age: 39,
    'Start date': '2009-09-15',
    Salary: '$205,500',
  },
  {
    Name: 'Tiger Nixon',
    Position: 'System Architect',
    Office: 'Edinburgh',
    Age: 61,
    'Start date': '2011-04-25',
    Salary: '$320,800',
  },
  {
    Name: 'Garrett Winters',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 63,
    'Start date': '2011-07-25',
    Salary: '$170,750',
  },
  {
    Name: 'Ashton Cox',
    Position: 'Junior Technical Author',
    Office: 'San Francisco',
    Age: 66,
    'Start date': '2009-01-12',
    Salary: '$86,000',
  },
  {
    Name: 'Cedric Kelly',
    Position: 'Senior Javascript Developer',
    Office: 'Edinburgh',
    Age: 22,
    'Start date': '2012-03-29',
    Salary: '$433,060',
  },
  {
    Name: 'Airi Satou',
    Position: 'Accountant',
    Office: 'Tokyo',
    Age: 33,
    'Start date': '2008-11-28',
    Salary: '$162,700',
  },
  {
    Name: 'Brielle Williamson',
    Position: 'Integration Specialist',
    Office: 'New York',
    Age: 61,
    'Start date': '2012-12-02',
    Salary: '$372,000',
  },
  {
    Name: 'Herrod Chandler',
    Position: 'Sales Assistant',
    Office: 'San Francisco',
    Age: 59,
    'Start date': '2012-08-06',
    Salary: '$137,500',
  },
  {
    Name: 'Rhona Davidson',
    Position: 'Integration Specialist',
    Office: 'Tokyo',
    Age: 55,
    'Start date': '2010-10-14',
    Salary: '$327,900',
  },
  {
    Name: 'Colleen Hurst',
    Position: 'Javascript Developer',
    Office: 'San Francisco',
    Age: 39,
    'Start date': '2009-09-15',
    Salary: '$205,500',
  },
];

export const project = [
  {
    taskName: 'Data Source Identification and Access',
    createDate: '15 June, 2024',
    assignees: [
      'assets/images/avatar/user-14.png',
      'assets/images/avatar/user-16.png',
    ],
    priority: 'High',
    status: 'New',
  },
  {
    taskName: 'Data Transformation',
    createDate: '08 April, 2024',
    assignees: [
      'assets/images/avatar/user-18.png',
      'assets/images/avatar/user-23.png',
      'assets/images/avatar/user-12.png',
    ],
    priority: 'Low',
    status: 'Pending',
  },
  {
    taskName: 'Cleaned and Transformed Datasets',
    createDate: '11 Feb, 2024',
    assignees: ['assets/images/avatar/user-16.png'],
    priority: 'Low',
    status: 'New',
  },
  {
    taskName: 'Data Quality Assurance',
    createDate: '22 May, 2024',
    assignees: [
      'assets/images/avatar/user-14.png',
      'assets/images/avatar/user-20.png',
      'assets/images/avatar/user-19.png',
    ],
    priority: 'High',
    status: 'Completed',
  },
  {
    taskName: 'Database Schema Design',
    createDate: '01 March, 2024',
    assignees: [
      'assets/images/avatar/user-13.png',
      'assets/images/avatar/user-21.png',
    ],
    priority: 'Low',
    status: 'New',
  },
  {
    taskName: 'Data Integration',
    createDate: '12 July, 2024',
    assignees: [
      'assets/images/avatar/user-22.png',
      'assets/images/avatar/user-24.png',
      'assets/images/avatar/user-15.png',
    ],
    priority: 'High',
    status: 'Completed',
  },
  {
    taskName: 'ETL Process Implementation',
    createDate: '30 April, 2024',
    assignees: [
      'assets/images/avatar/user-25.png',
      'assets/images/avatar/user-11.png',
    ],
    priority: 'Low',
    status: 'Pending',
  },
  {
    taskName: 'Performance Tuning',
    createDate: '18 March, 2024',
    assignees: [
      'assets/images/avatar/user-14.png',
      'assets/images/avatar/user-23.png',
    ],
    priority: 'High',
    status: 'New',
  },
  {
    taskName: 'Security and Compliance Checks',
    createDate: '05 May, 2024',
    assignees: [
      'assets/images/avatar/user-18.png',
      'assets/images/avatar/user-20.png',
      'assets/images/avatar/user-19.png',
    ],
    priority: 'High',
    status: 'Completed',
  },
  {
    taskName: 'User Access Management',
    createDate: '10 August, 2024',
    assignees: [
      'assets/images/avatar/user-30.png',
      'assets/images/avatar/user-29.png',
    ],
    priority: 'High',
    status: 'Pending',
  },
  {
    taskName: 'Data Backup and Recovery',
    createDate: '02 September, 2024',
    assignees: [
      'assets/images/avatar/user-26.png',
      'assets/images/avatar/user-28.png',
    ],
    priority: 'Low',
    status: 'Pending',
  },
  {
    taskName: 'Data Archival Strategy',
    createDate: '20 March, 2024',
    assignees: [
      'assets/images/avatar/user-21.png',
      'assets/images/avatar/user-27.png',
      'assets/images/avatar/user-30.png',
    ],
    priority: 'Low',
    status: 'New',
  },
  {
    taskName: 'Data Visualization',
    createDate: '15 July, 2024',
    assignees: [
      'assets/images/avatar/user-25.png',
      'assets/images/avatar/user-18.png',
    ],
    priority: 'High',
    status: 'Pending',
  },
  {
    taskName: 'Machine Learning Model Training',
    createDate: '28 August, 2024',
    assignees: [
      'assets/images/avatar/user-19.png',
      'assets/images/avatar/user-14.png',
      'assets/images/avatar/user-16.png',
    ],
    priority: 'High',
    status: 'New',
  },
];
