<div class="sticky top-24 card" [formGroup]="formGroup" *ngIf="formGroup">
    <div class="card-header">
        <h6 class="card-title">Add New Category</h6>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12">
                <div>
                    <label for="logo" class="flex items-center justify-center p-2 mx-auto overflow-hidden bg-gray-100 border border-gray-200 rounded cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-32">
                        <img *ngIf="formGroup.get('image')?.value" [src]="formGroup.get('image')?.value" class="object-cover w-full h-full">
                        <span *ngIf="!formGroup.get('image')?.value" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                            <lucide-angular name="upload"></lucide-angular>
                            <span class="block mt-2">Upload Images</span>
                        </span>
                    </label>
                    <span *ngIf="formGroup.get('image')?.hasError('required')" class="text-red-500">This Field is required</span>
                    <div class="hidden mt-4">
                        <label class="block">
                            <span class="sr-only">Choose profile photo</span>
                            <input type="file" name="logo" id="logo" (change)="onFileChange($event)" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" formControlName="image" />
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-span-12">
                <label for="categoryNameInput" class="form-label">Category Name</label>
                <input type="text" id="categoryNameInput" class="form-input" placeholder="Category name" formControlName="category">
                <span *ngIf="formGroup.get('category')?.hasError('required')" class="text-red-500">This Field is required</span>
            </div>
            <div class="col-span-12">
                <label for="categoryNameInput" class="form-label">Product</label>
                <input class="form-input" placeholder="Enter product" formControlName="products">
                <span *ngIf="formGroup.get('products')?.hasError('required')" class="text-red-500">This Field is required</span>
            </div>
            <div class="col-span-12">
                <label for="descriptionTextarea" class="form-label">Description</label>
                <textarea name="descriptionTextarea" id="descriptionTextarea" rows="3" class="h-auto form-input" placeholder="Enter your description" formControlName="description"></textarea>
                <span *ngIf="formGroup.get('description')?.hasError('required')" class="text-red-500">This Field is required</span>
            </div>
            <div class="col-span-12">
                <label for="statusSelect" class="form-label">Status</label>
                <select id="statusSelect" class="form-input" formControlName="status">
                    <option [value]="true">Active</option>
                    <option [value]="false">Inactive</option>
                </select>
                <span *ngIf="formGroup.get('status')?.hasError('required')" class="text-red-500">This Field is required</span>
            </div>
            <div class="flex items-center justify-end col-span-12 gap-2">
                <button class="btn btn-sub-gray" (click)="reset()"><lucide-angular name="rotate-ccw" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></lucide-angular> <span class="align-middle">Reset</span></button>
                <button class="btn btn-primary" (click)="submit()"><lucide-angular name="plus" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></lucide-angular> <span class="align-middle">Add Category</span></button>
            </div>
        </div>
    </div>
</div>