{"name": "domiex", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:domiex": "node dist/domiex/server/server.mjs"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^18.0.0", "@angular/animations": "^20.0.4", "@angular/common": "^20.0.4", "@angular/compiler": "^20.0.4", "@angular/core": "^20.0.4", "@angular/forms": "^20.0.4", "@angular/google-maps": "^18.2.5", "@angular/platform-browser": "^20.0.4", "@angular/platform-browser-dynamic": "^20.0.4", "@angular/platform-server": "^20.0.4", "@angular/router": "^20.0.4", "@angular/ssr": "^20.0.3", "@ckeditor/ckeditor5-angular": "^6.0.0", "@ckeditor/ckeditor5-build-classic": "^38.1.1", "@ctrl/ngx-emoji-mart": "^9.2.0", "@fullcalendar/angular": "^6.1.15", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/multimonth": "^6.1.15", "@fullcalendar/resource-timeline": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@ng-select/ng-select": "^13.7.0", "@ngrx/effects": "^18.0.2", "@ngrx/entity": "^18.0.2", "@ngrx/store": "^18.0.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@tailwindcss/forms": "^0.5.7", "@tanstack/angular-table": "^8.20.5", "@types/nouislider": "^15.0.0", "ag-grid-angular": "^32.1.0", "ag-grid-community": "^32.1.0", "angularx-flatpickr": "^8.0.0", "aos": "^2.3.4", "apexcharts": "^3.52.0", "apexsankey": "^1.1.2", "apextree": "^1.3.0", "boxicons": "^2.1.4", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.4", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "echarts": "^5.5.1", "express": "^4.18.2", "flatpickr": "^4.6.13", "img-comparison-slider": "^8.0.6", "jsvectormap": "^1.6.0", "line-awesome": "^1.3.0", "lodash-es": "^4.17.21", "lucide-angular": "^0.429.0", "ng-apexcharts": "^1.11.0", "ng2-charts": "^6.0.1", "ngx-countup": "^13.2.0", "ngx-drag-drop": "^18.0.2", "ngx-editor": "^18.0.0", "ngx-highlightjs": "^12.0.0", "ngx-mask": "^18.0.0", "ngx-pagination": "^6.0.3", "ngx-tooltip-directives": "^18.0.1", "pixeleyezui": "^1.0.1", "prismjs": "^1.29.0", "remixicon": "^4.3.0", "rxjs": "~7.8.0", "simplebar-angular": "^3.2.6", "swiper": "^11.0.5", "tslib": "^2.3.0", "uuid": "^10.0.0", "vanilla-tilt": "^1.8.1", "wnumb": "^1.2.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.4", "@angular/localize": "^20.0.4", "@types/aos": "^3.0.7", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@types/prismjs": "^1.26.4", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}}