<div class="relative mb-6">
  <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
    <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96">
    </div>
    <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
    <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
  </div>
  <div class="text-center">
    <div class="relative inline-block mx-auto">
      <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
        <img src="assets/images/avatar/user-14.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
      </div>
      <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5">
      </div>
    </div>
    <h5 class="mt-2 mb-1">Danny Carroll
      <lucide-angular name="BadgeCheck" class="inline-block text-primary-500 fill-primary-500/20 size-5">
      </lucide-angular>
    </h5>
    <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
      <li>
        <lucide-angular name="Building2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">UI /
          UX Designer</span>
      </li>
      <li>
        <lucide-angular name="MapPin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">Argentina</span>
      </li>
      <li>
        <lucide-angular name="CalendarDays" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">24 April, 2024</span>
      </li>
    </ul>
  </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
  <li>
    <a routerLink="/pages-user" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="Eye" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Overview</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-activity" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="sparkles" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Activity</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-followers" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
      <lucide-angular name="UserRound" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Followers</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-documents" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="FileText" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Documents</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-notes" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
      <lucide-angular name="list" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Notes</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-projects" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
      <lucide-angular name="monitor" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Projects</span>
    </a>
  </li>
</ul>

<div class="grid grid-cols-1 mt-5 lg:grid-cols-2 gap-x-5">

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-13.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Christina Williams</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">christina&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">+(546)
              01234 567 89</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive" class="flex items-center gap-2"><i class="ri-user-add-line"></i> Follow</span>
        <span *ngIf="isActive" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> UnFollow</span>
        <svg *ngIf="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-14.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Thomas Blamer</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">thomas&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle2()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive2" class="flex items-center gap-2"><i class="ri-user-add-line"></i> UnFollow</span>
        <span *ngIf="isActive2" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> Follow</span>
        <svg *ngIf="loadingButton2" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-15.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Patricia Graham</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">pg&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle3()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive3" class="flex items-center gap-2"><i class="ri-user-add-line"></i> UnFollow</span>
        <span *ngIf="isActive3" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> Follow</span>
        <svg *ngIf="loadingButton2" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-19.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Patricia Graham</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">patricia&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle4()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive4" class="flex items-center gap-2"><i class="ri-user-add-line"></i> Follow</span>
        <span *ngIf="isActive4" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> UnFollow</span>
        <svg *ngIf="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-20.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Joseph Obrien</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">josepho&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle5()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive5" class="flex items-center gap-2"><i class="ri-user-add-line"></i> Follow</span>
        <span *ngIf="isActive5" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> UnFollow</span>
        <svg *ngIf="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-45.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Edward Chapman</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">edward&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle6()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive6" class="flex items-center gap-2"><i class="ri-user-add-line"></i> UnFollow</span>
        <span *ngIf="isActive6" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> Follow</span>
        <svg *ngIf="loadingButton2" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-38.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Annie Akins</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">annie&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle7()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive7" class="flex items-center gap-2"><i class="ri-user-add-line"></i> UnFollow</span>
        <span *ngIf="isActive7" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> Follow</span>
        <svg *ngIf="loadingButton2" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

  <div class="card">
    <div class="flex flex-wrap items-center gap-3 card-body">
      <div class="shrink-0">
        <img src="assets/images/avatar/user-35.png" alt="" class="rounded-md size-20">
      </div>
      <div class="grow">
        <h6 class="mb-1">Gena Kelly</h6>
        <div class="flex flex-wrap items-center gap-4 mb-3">
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap">kelly&#64;srbthemes.com</a>
          </p>
          <p class="text-gray-500 dark:text-dark-500">
            <lucide-angular name="Phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">************</span>
          </p>
        </div>
        <a href="javascript: void(0);" class="text-primary-500">
          View More
          <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
          <lucide-angular name="MoveLeft" class="mr-1 rtl:inline-block ltr:hidden size-4"></lucide-angular>
        </a>
      </div>
      <button (click)="toggle8()" class="btn btn-sub-gray btn-icon-text">
        <span *ngIf="!isActive8" class="flex items-center gap-2"><i class="ri-user-add-line"></i> UnFollow</span>
        <span *ngIf="isActive8" class="flex items-center gap-2"><i class="ri-user-unfollow-line"></i> Follow</span>
        <svg *ngIf="loadingButton2" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </button>
    </div>
  </div>

</div>

<div class="grid grid-cols-12 gap-5 mb-5">
  <div class="col-span-12 md:col-span-6">
    <p class="text-gray-500 dark:text-dark-500">Showing <b>8</b> of <b>76</b> Results</p>
  </div>

  <div class="col-span-12 md:col-span-6">
    <div class="flex pagination pagination-primary md:justify-end">
      <button type="button" class="pagination-pre" disabled>
        <lucide-angular name="ChevronLeft" class="mr-1 ltr:inline-block rtl:hidden size-5"></lucide-angular>
        <lucide-angular name="ChevronRight" class="ml-1 ltr:hidden rtl:inline-block size-5"></lucide-angular>
        Prev
      </button>
      <button type="button" class="pagination-item active">1</button>
      <button type="button" class="pagination-item">2</button>
      <button type="button" class="pagination-item">3</button>
      <button type="button" class="pagination-item">...</button>
      <button type="button" class="pagination-item">10</button>
      <button type="button" class="pagination-next">
        Next
        <lucide-angular name="ChevronRight" class="ml-1 rtl:hidden size-5 ltr:inline-block"></lucide-angular>
        <lucide-angular name="ChevronLeft" class="mr-1 rtl:inline-block ltr:hidden size-5"></lucide-angular>
      </button>
    </div>
  </div>

</div>