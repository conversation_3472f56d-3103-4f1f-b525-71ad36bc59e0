// questions-data.ts

export const questionsData = [
  {
    id: 1,
    question: 'What is an important factor of management information system?',
    options: ['Data', 'System', 'Process', 'All of the above'],
    type: 'MCQ',
    difficulty: 'Hard',
    status: 'New',
  },
  {
    id: 2,
    question: 'The data Flow Diagram is the basic component of …………… system',
    options: ['Conceptual', 'Logical', 'Physical', 'Sequential'],
    type: 'MCQ',
    difficulty: 'Hard',
    status: 'New',
  },
  {
    id: 3,
    question: 'A desirable property of module is',
    options: [
      'Independency',
      'Low Cohesiveness',
      'High Coupling',
      'Multi Functional',
    ],
    type: 'MCQ',
    difficulty: 'Hard',
    status: 'New',
  },
  {
    id: 4,
    question: 'Which of the following UML diagrams has a static view?',
    options: [
      'Collaboration Diagram',
      'Use-Case Diagram',
      'State chart Diagram',
      'Activity Diagram',
    ],
    type: 'MCQ',
    difficulty: 'Medium',
    status: 'Old',
  },
  {
    id: 5,
    question: 'What is the full form of HTML?',
    options: [
      'Hyper text markup language',
      'Hyphenation text markup language',
      'Hyphenation test marking language',
      'Hyper text marking language',
    ],
    type: 'MCQ',
    difficulty: 'Hard',
    status: 'New',
  },
  {
    id: 6,
    question:
      'What is the role of the Just-In-Time (JIT) compiler in the .NET Framework?',
    options: [
      'To translate source code into intermediate language (IL)',
      'To compile IL code into native code at runtime',
      'To optimize IL code for better performance',
      'To manage memory and resources',
    ],
    type: 'MCQ',
    difficulty: 'Medium',
    status: 'Old',
  },
];
