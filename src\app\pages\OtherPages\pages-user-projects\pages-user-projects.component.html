<div class="relative mb-6">
  <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
    <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96">
    </div>
    <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
    <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
  </div>
  <div class="text-center">
    <div class="relative inline-block mx-auto">
      <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
        <img src="assets/images/avatar/user-14.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
      </div>
      <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5">
      </div>
    </div>
    <h5 class="mt-2 mb-1">Danny Carroll
      <lucide-angular name="BadgeCheck" class="inline-block text-primary-500 fill-primary-500/20 size-5">
      </lucide-angular>
    </h5>
    <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
      <li>
        <lucide-angular name="Building2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">UI /
          UX Designer</span>
      </li>
      <li>
        <lucide-angular name="MapPin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">Argentina</span>
      </li>
      <li>
        <lucide-angular name="CalendarDays" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> <span class="align-middle">24 April, 2024</span>
      </li>
    </ul>
  </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
  <li>
    <a routerLink="/pages-user" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="Eye" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Overview</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-activity" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="sparkles" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Activity</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-followers" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="UserRound" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Followers</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-documents" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="FileText" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Documents</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-notes" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 ">
      <lucide-angular name="list" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Notes</span>
    </a>
  </li>
  <li>
    <a routerLink="/pages-user-projects" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
      <lucide-angular name="monitor" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></lucide-angular> <span class="align-middle whitespace-nowrap">Projects</span>
    </a>
  </li>
</ul>

<h5 class="mt-4 mb-5 text-16">Projects</h5>

<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-5">
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-sky-500/10 rounded-modern shrink-0">
          <lucide-angular name="MessagesSquare" class="relative stroke-1 text-sky-500 size-7 fill-sky-500/10">
          </lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Chat App
              Templates</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">Chat applications typically run on centralized
            networks that are served by platform operator servers as opposed to peer-to-peer protocols such as XMPP.
            This allows two people to talk to each other in real time.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Features</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">148</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 dark:border-dark-800 size-7" src="assets/images/avatar/user-2.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 dark:border-dark-800 size-7" src="assets/images/avatar/user-3.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown1">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="!fixed p-2 dropdown-menu dropdown-right" #dropdown1>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-pink-500/10 rounded-modern shrink-0">
          <lucide-angular name="box" class="relative text-pink-500 stroke-1 size-7 fill-pink-500/10"></lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Domiex -
              Admin & Dashboards Templates</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">An admin dashboard template is a powerful tool that
            streamlines the process of building a robust and user-friendly admin panel for web applications. Image by
            ThemeMakker. It provides a pre-designed interface with various components and features to manage and monitor
            the backend of an application.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Admin</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">74</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-5.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-20.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-13.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown2">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="p-2 dropdown-menu dropdown-right" #dropdown2>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-green-500/10 rounded-modern shrink-0">
          <lucide-angular name="Users" class="relative text-green-500 stroke-1 size-7 fill-green-500/10">
          </lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Employee
              Management System</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">Employee management is the process by which employers
            ensure workers perform their jobs to the best of their abilities so as to achieve business goals. It
            typically entails building and maintaining healthy relationships with employees, as well as monitoring their
            daily labor and measuring progress.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Management</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">179</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-15.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-16.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-17.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-18.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-22.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown3">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="p-2 dropdown-menu dropdown-right" #dropdown3>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-purple-500/10 rounded-modern shrink-0">
          <lucide-angular name="globe" class="relative text-purple-500 stroke-1 size-7 fill-purple-500/10">
          </lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Create
              Business Website</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">If all you need is a basic one-page website with an
            email address, phone number and maybe your business address, you can absolutely do that yourself. There are
            many website builders and one-page templates you can use to get up and running quickly.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Design</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">132</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-10.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown3">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="p-2 dropdown-menu dropdown-right" #dropdown3>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-primary-500/10 rounded-modern shrink-0">
          <lucide-angular name="squareUser" class="relative stroke-1 text-primary-500 size-7 fill-primary-500/10">
          </lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Contact Page
              Prototype</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">A contact page prototype is a draft of a contact
            page. It can be used to test the design and functionality of the page before it is launched. There are many
            different ways to create a contact page prototype. Some popular methods include using a wireframe tool, a
            prototyping tool, or a content management system.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Figma</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">163</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-20.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-18.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-22.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-23.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown4">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="p-2 dropdown-menu dropdown-right" #dropdown4>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
  <div class="card">
    <div class="card-body">
      <div class="flex gap-3">
        <div class="flex items-center justify-center size-12 bg-gradient-to-t from-red-500/10 rounded-modern shrink-0">
          <lucide-angular name="ruler" class="relative text-red-500 stroke-1 size-7 fill-red-500/10"></lucide-angular>
        </div>
        <div class="grow">
          <h6 class="mb-1"><a href="javascript: void(0);" class="transition duration-200 ease-linear hover:text-primary-500">Design System
              - Create Components</a></h6>
          <p class="text-gray-500 dark:text-dark-500 line-clamp-5">A design system defines reusable components and their
            usage, and explains why and when designers should choose a component. This helps designers and developers
            learn design concepts and best practices for different components.</p>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-3">
        <a href="javascript: void(0);" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Components</a>
        <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
          <p>
            <lucide-angular name="Eye" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></lucide-angular> <span class="align-middle">245</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
      <div class="flex -space-x-3 grow rtl:space-x-reverse">
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-10.png" alt=""></a>
        <a href="javascript: void(0);" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-14.png" alt=""></a>
      </div>
      <div class="dropdown">
        <button x-ref="button" type="button" title="dropdown-button" class="link link-primary" domixDropdownToggle [dropdownMenu]="dropdown5">
          <lucide-angular name="ellipsis" class="size-5"></lucide-angular>
        </button>

        <div class="p-2 dropdown-menu dropdown-right" #dropdown5>
          <a href="javascript: void(0);" class="dropdown-item">
            Overview
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Edit
          </a>

          <a href="javascript: void(0);" class="dropdown-item">
            Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  <!--end card-->
</div>


<div class="grid items-center grid-cols-12 gap-5 mb-5">
  <div class="col-span-6">
    <p class="text-gray-500 dark:text-dark-500">Showing <b>6</b> of <b>76</b> Results</p>
  </div>

  <div class="col-span-6">
    <div class="flex justify-end pagination pagination-primary">
      <button type="button" class="pagination-pre" disabled>
        <lucide-angular name="ChevronLeft" class="mr-1 ltr:inline-block rtl:hidden size-5"></lucide-angular>
        <lucide-angular name="ChevronRight" class="ml-1 ltr:hidden rtl:inline-block size-5"></lucide-angular>
        Prev
      </button>
      <button type="button" class="pagination-item active">1</button>
      <button type="button" class="pagination-item">2</button>
      <button type="button" class="pagination-item">3</button>
      <button type="button" class="pagination-item">...</button>
      <button type="button" class="pagination-item">10</button>
      <button type="button" class="pagination-next">
        Next
        <lucide-angular name="ChevronRight" class="ml-1 rtl:hidden size-5 ltr:inline-block"></lucide-angular>
        <lucide-angular name="ChevronLeft" class="mr-1 rtl:inline-block ltr:hidden size-5"></lucide-angular>
      </button>
    </div>
  </div>

</div>