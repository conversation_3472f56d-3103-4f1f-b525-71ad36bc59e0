<div
  class="grid grid-cols-12 gap-0 from-sky-500/10 ltr:bg-gradient-to-l rtl:bg-gradient-to-r via-green-500/5 to-pink-500/5">
  <div class="col-span-7">
    <div class="flex items-center h-screen border-gray-200 ltr:border-r rtl:border-l dark:border-dark-800">
      <div class="px-20 grow">
        <h1 class="w-2/3 mb-10">What expertise should specialists possess to develop your project?</h1>
        <div class="grid grid-cols-4 gap-5 mb-10">
          <div class="relative input-check-group">
            <input id="brandingInput"
              class="absolute z-10 input-check input-check-primary peer ltr:right-4 rtl:left-4 top-4" type="checkbox" />
            <label for="brandingInput"
              class="relative block p-4 bg-white border border-gray-200 rounded-md shadow-lg cursor-pointer dark:border-dark-800 dark:bg-dark-900 peer-checked:bg-primary-100 dark:peer-checked:bg-primary-500/10 peer-checked:border-primary-300 dark:peer-checked:border-primary-500/30 shadow-gray-100 dark:shadow-dark-850">
              <span class="flex items-center justify-center rounded-md text-primary-50 bg-primary-500 size-10">
                <lucide-angular name="Gem" class="size-5"></lucide-angular>
              </span>
              <span class="block mt-4 mb-1 text-base font-medium">Branding</span>
              <span class="block text-sm text-gray-500 dark:text-dark-500">We will create the image of your product from
                scratch.</span>
            </label>
          </div>

          <div class="relative input-check-group">
            <input id="productCheckbox"
              class="absolute z-10 input-check input-check-green peer ltr:right-4 rtl:left-4 top-4" type="checkbox" />
            <label for="productCheckbox"
              class="relative block p-4 bg-white border border-gray-200 rounded-md shadow-lg cursor-pointer dark:border-dark-800 dark:bg-dark-900 peer-checked:bg-green-100 dark:peer-checked:bg-green-500/10 peer-checked:border-green-300 dark:peer-checked:border-green-500/30 shadow-gray-100 dark:shadow-dark-850">
              <span class="flex items-center justify-center bg-green-500 rounded-md text-green-50 size-10">
                <lucide-angular name="layers3" class="size-5"></lucide-angular>
              </span>
              <span class="block mt-4 mb-1 text-base font-medium">Product Design</span>
              <span class="block text-sm text-gray-500 dark:text-dark-500">We will create the image of your product from
                scratch.</span>
            </label>
          </div>

          <div class="relative input-check-group">
            <input id="developmentCheckbox"
              class="absolute z-10 input-check input-check-yellow peer ltr:right-4 rtl:left-4 top-4" type="checkbox" />
            <label for="developmentCheckbox"
              class="relative block p-4 bg-white border border-gray-200 rounded-md shadow-lg cursor-pointer dark:border-dark-800 dark:bg-dark-900 peer-checked:bg-yellow-100 dark:peer-checked:bg-yellow-500/10 peer-checked:border-yellow-300 dark:peer-checked:border-yellow-500/30 shadow-gray-100 dark:shadow-dark-850">
              <span class="flex items-center justify-center bg-yellow-500 rounded-md text-yellow-50 size-10">
                <lucide-angular name="MonitorStop" class="size-5"></lucide-angular>
              </span>
              <span class="block mt-4 mb-1 text-base font-medium">Web Development</span>
              <span class="block text-sm text-gray-500 dark:text-dark-500">We will create the image of your product from
                scratch.</span>
            </label>
          </div>

          <div class="relative input-check-group">
            <input id="customizeCheckbox"
              class="absolute z-10 input-check input-check-purple peer ltr:right-4 rtl:left-4 top-4" type="checkbox" />
            <label for="customizeCheckbox"
              class="relative block p-4 bg-white border border-gray-200 rounded-md shadow-lg cursor-pointer dark:border-dark-800 dark:bg-dark-900 peer-checked:bg-purple-100 dark:peer-checked:bg-purple-500/10 peer-checked:border-purple-300 dark:peer-checked:border-purple-500/30 shadow-gray-100 dark:shadow-dark-850">
              <span class="flex items-center justify-center bg-purple-500 rounded-md text-purple-50 size-10">
                <lucide-angular name="pencilRuler" class="size-5"></lucide-angular>
              </span>
              <span class="block mt-4 mb-1 text-base font-medium">Customize Projects</span>
              <span class="block text-sm text-gray-500 dark:text-dark-500">We will create the image of your product from
                scratch.</span>
            </label>
          </div>

        </div>

        <div class="flex gap-2">
          <a routerLink="/pages-contact-us-two" class="btn btn-outline-gray">
            <lucide-angular name="MoveLeft" class="mr-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
            <lucide-angular name="MoveRight" class="ml-1 ltr:hidden rtl:inline-block size-4"></lucide-angular>
            Back
          </a>
          <a routerLink="/pages-contact-us-four" class="btn btn-primary">
            Go Forward
            <lucide-angular name="MoveRight" class="ml-1 ltr:inline-block rtl:hidden size-4"></lucide-angular>
            <lucide-angular name="MoveLeft" class="mr-1 ltr:hidden rtl:inline-block size-4"></lucide-angular>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div class="col-span-5">
    <div class="flex flex-col justify-center w-full h-screen px-20">
      <div>
        <h5 class="mb-3">At each stage of the project timeline, we ensure quality through the dedicated oversight of our
          Head of Design, who leads our quality assurance efforts.</h5>
        <p class="mb-5 text-gray-500 dark:text-dark-500">Our Head of Design is entrusted with ensuring the utmost
          quality of the final digital product.</p>
        <ul class="space-y-2 mb-7">
          <li>
            <lucide-angular name="CornerUpRight" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4">
            </lucide-angular>
            <lucide-angular name="CornerUpLeft" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4">
            </lucide-angular>
            Basic support at the start of the project
          </li>
          <li>
            <lucide-angular name="CornerUpRight" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4">
            </lucide-angular>
            <lucide-angular name="CornerUpLeft" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4">
            </lucide-angular>
            Taking care of the best design quality
          </li>
          <li>
            <lucide-angular name="CornerUpRight" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4">
            </lucide-angular>
            <lucide-angular name="CornerUpLeft" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4">
            </lucide-angular>
            Perfect pixel at every stage of building your project
          </li>
          <li>
            <lucide-angular name="CornerUpRight" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4">
            </lucide-angular>
            <lucide-angular name="CornerUpLeft" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4">
            </lucide-angular>
            Support: Design, Creative & Development
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
