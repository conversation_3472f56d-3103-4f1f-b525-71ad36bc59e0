<app-page-title [pageTitle]="'Ecommerce'" [Title]="'Manage Reviews'"></app-page-title>


<div class="grid items-center grid-cols-12 gap-5 mb-5">
  <div class="col-span-12 2xl:col-span-8">
    <h6 class="card-title">Reviews</h6>
  </div>
  <div class="col-span-12 2xl:col-span-4">
    <div class="flex flex-wrap items-center gap-3 2xl:justify-end">
      <div class="relative group/form">
        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search username, date, etc..." [(ngModel)]="searchText" (ngModelChange)="updateDisplayedData()"><span class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-none">
          <lucide-angular name="Search" class="size-4"></lucide-angular>
        </span>
      </div>
      <button class="btn btn-primary" (click)="addReviewModal(null,null)">
        <lucide-angular name="Plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></lucide-angular> New Review
      </button>
    </div>
  </div>
</div>

<div>
  <div class="grid items-center grid-cols-1 mb-4 divide-y divide-gray-200 dark:divide-dark-800 xl:grid-cols-3 xl:divide-y-0 xl:divide-x rtl:xl:divide-x-reverse">
    <div class="p-5 xl:ltr:first:pl-0 xl:rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
      <h6 class="mb-5">Total Reviews</h6>
      <h3>{{ gridData.length }} <span class="inline-block text-xs align-middle badge badge-green">100%</span></h3>
      <p class="mt-2 text-gray-500 dark:text-dark-500">Growth in reviews on this year</p>
    </div>
    <div class="p-5 ltr:first:pl-0 rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
      <h6 class="mb-5">Average Rating</h6>
      <div class="flex items-center gap-3">
        <h3>{{ averageReview }}</h3>
        <div class="text-yellow-500">
          <ng-container *ngFor="let i of [1, 2, 3, 4, 5]">
            <i [ngClass]="getStarClass(averageReview,i)"></i>
          </ng-container>
        </div>
      </div>
      <p class="mt-2 text-gray-500">Average rating on this year</p>
    </div>
    <div class="p-5 space-y-1 ltr:first:pl-0 rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
      <div class="flex items-center gap-2">
        <p class="shrink-0">
          <i class="text-yellow-500 ri-star-fill"></i> 5
        </p>
        <div class="w-[80%] progress-bar progress-1">
          <div class="w-full text-white bg-green-500 progress-bar-wrap"></div>
        </div>
        <h6>4751</h6>
      </div>
      <div class="flex items-center gap-2">
        <p class="shrink-0">
          <i class="text-yellow-500 ri-star-fill"></i> 4
        </p>
        <div class="w-[64%] progress-bar progress-1">
          <div class="w-full text-white bg-pink-500 progress-bar-wrap"></div>
        </div>
        <h6>3658</h6>
      </div>
      <div class="flex items-center gap-2">
        <p class="shrink-0">
          <i class="text-yellow-500 ri-star-fill"></i> 3
        </p>
        <div class="w-[51%] progress-bar progress-1">
          <div class="w-full text-white bg-yellow-500 progress-bar-wrap"></div>
        </div>
        <h6>2349</h6>
      </div>
      <div class="flex items-center gap-2">
        <p class="shrink-0">
          <i class="text-yellow-500 ri-star-fill"></i> 2
        </p>
        <div class="w-[38%] progress-bar progress-1">
          <div class="w-full text-white bg-sky-500 progress-bar-wrap"></div>
        </div>
        <h6>1472</h6>
      </div>
      <div class="flex items-center gap-2">
        <p class="shrink-0">
          <i class="text-yellow-500 ri-star-fill"></i> 1
        </p>
        <div class="w-[15%] progress-bar progress-1">
          <div class="w-full text-white bg-red-500 progress-bar-wrap"></div>
        </div>
        <h6>120</h6>
      </div>
    </div>
  </div>


  <div class="!overflow-x-auto">
    <table class="table flush whitespace-nowrap !overflow-x-auto">
      <tbody>
        <ng-container *ngIf="displayedData.length > 0">
          <ng-container *ngFor="let review of displayedData; let i = index">
            <tr class="gap-2">
              <td class="align-top whitespace-nowrap">
                <div class="flex items-center gap-3">
                  <img [src]="review.image" alt="" class="rounded-md shrink-0 size-16">
                  <div class="overflow-hidden grow">
                    <h6 class="mb-1 truncate">
                      <a href="javascript: void(0);" class="text-current link link-primary">{{review.userName}}</a>
                    </h6>
                    <p class="mb-1 text-sm truncate">{{review.date}}</p>
                    <p class="text-sm text-gray-500 truncate dark:text-dark-500">Location: <span>{{review.location}}</span></p>
                  </div>
                </div>
              </td>
              <td class="whitespace-nowrap">
                <div class="max-w-[550px]">
                  <div class="flex items-center gap-2 mb-3">
                    <div class="text-yellow-500">
                      <i class="text-yellow-500 fa fa-star"></i>
                      <ng-container *ngFor="let i of [1, 2, 3, 4, 5]">
                        <i [ngClass]="getStarClass(review.star,i)"></i>
                      </ng-container>
                    </div>
                    <h6>(<span>{{review.star}}</span>)</h6>
                  </div>
                  <h6 class="mb-1">{{review.title}}</h6>
                  <p class="text-gray-500 whitespace-normal dark:text-dark-500">{{review.content}}</p>
                </div>
              </td>
              <td class="align-top">
                <div class="flex items-center justify-end gap-3">
                  <button class="btn btn-sub-gray">Direct Message</button>
                  <div class="dropdown">
                    <button domixDropdownToggle [dropdownMenu]="dropdown2" type="button" class="btn btn-icon-text btn-primary btn-icon">
                      <i class="ri-more-2-fill"></i>
                    </button>
                    <div #dropdown2 class="!fixed p-2 dropdown-menu">
                      <ul>
                        <li>
                          <a href="javascript: void(0);" data-modal-target="" (click)="addReviewModal(review,i)" class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                          </a>
                        </li>
                        <li>
                          <a href="javascript: void(0);" (click)="deleteModal(i)" class="dropdown-item">
                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="displayedData.length == 0">
          <td colspan="10" class="!p-8">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
              <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#60e8fe"></stop>
                <stop offset=".033" stop-color="#6ae9fe"></stop>
                <stop offset=".197" stop-color="#97f0fe"></stop>
                <stop offset=".362" stop-color="#bdf5ff"></stop>
                <stop offset=".525" stop-color="#dafaff"></stop>
                <stop offset=".687" stop-color="#eefdff"></stop>
                <stop offset=".846" stop-color="#fbfeff"></stop>
                <stop offset="1" stop-color="#fff"></stop>
              </linearGradient>
              <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
              </path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
              </path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
            </svg>
            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
          </td>
        </ng-container>
      </tbody>
    </table>
  </div>
  <app-domix-pagination [currentPage]="currentPage" [totalPages]="totalPages" [showingStart]="showingStart" [showingEnd]="showingEnd" [totalResults]="gridData.length" (pageChanged)="onPageChange($event)">
  </app-domix-pagination>
</div>